<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأخطاء - ركن الأطفال</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .debug-panel {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            margin: 5px;
            cursor: pointer;
        }
        .test-btn:hover {
            background: #5a67d8;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: #f0fff4;
            border-color: #38a169;
            color: #2f855a;
        }
        .error {
            background: #fff5f5;
            border-color: #e53e3e;
            color: #c53030;
        }
        .warning {
            background: #fffbeb;
            border-color: #d69e2e;
            color: #b7791f;
        }
        .info {
            background: #ebf8ff;
            border-color: #3182ce;
            color: #2c5282;
        }
        #console-output {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mx-auto">
        <h1 class="text-3xl font-bold text-white text-center mb-8">
            🐛 اختبار وإصلاح الأخطاء
        </h1>

        <!-- Console Output -->
        <div class="debug-panel">
            <h2 class="text-xl font-bold mb-4">📟 مخرجات وحدة التحكم</h2>
            <div id="console-output"></div>
            <button class="test-btn" onclick="clearConsole()">مسح الوحدة</button>
        </div>

        <!-- File Tests -->
        <div class="debug-panel">
            <h2 class="text-xl font-bold mb-4">📁 اختبار الملفات</h2>
            <button class="test-btn" onclick="testFile('interactive-story-maker.html')">صانع القصص</button>
            <button class="test-btn" onclick="testFile('collaborative-writing.html')">الكتابة التعاونية</button>
            <button class="test-btn" onclick="testFile('advanced-customization.html')">التخصيص المتقدم</button>
            <div id="file-results"></div>
        </div>

        <!-- JavaScript Tests -->
        <div class="debug-panel">
            <h2 class="text-xl font-bold mb-4">⚙️ اختبار JavaScript</h2>
            <button class="test-btn" onclick="testLocalStorage()">التخزين المحلي</button>
            <button class="test-btn" onclick="testDataCollection()">جمع البيانات</button>
            <button class="test-btn" onclick="testStoryGeneration()">توليد القصص</button>
            <div id="js-results"></div>
        </div>

        <!-- Navigation Tests -->
        <div class="debug-panel">
            <h2 class="text-xl font-bold mb-4">🔗 اختبار التنقل</h2>
            <button class="test-btn" onclick="testNavigation()">اختبار الروابط</button>
            <button class="test-btn" onclick="simulateCollaboration()">محاكاة التعاون</button>
            <div id="nav-results"></div>
        </div>

        <!-- Fix Issues -->
        <div class="debug-panel">
            <h2 class="text-xl font-bold mb-4">🔧 إصلاح المشاكل</h2>
            <button class="test-btn" onclick="fixCommonIssues()">إصلاح المشاكل الشائعة</button>
            <button class="test-btn" onclick="resetAllData()">إعادة تعيين البيانات</button>
            <div id="fix-results"></div>
        </div>
    </div>

    <script>
        // Override console methods to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function logToPanel(message, type = 'log') {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const color = {
                log: '#e2e8f0',
                error: '#fc8181',
                warn: '#f6e05e',
                info: '#63b3ed'
            }[type];
            
            output.innerHTML += `<div style="color: ${color}">[${timestamp}] ${type.toUpperCase()}: ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            logToPanel(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            logToPanel(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToPanel(args.join(' '), 'warn');
        };

        function clearConsole() {
            document.getElementById('console-output').innerHTML = '';
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation-triangle' : 'info'} ml-2"></i>
                ${message}
            `;
            container.appendChild(result);
        }

        function testFile(filename) {
            console.log(`اختبار ملف: ${filename}`);
            
            fetch(filename)
                .then(response => {
                    if (response.ok) {
                        console.log(`✅ ${filename} موجود ويمكن الوصول إليه`);
                        showResult('file-results', `${filename} يعمل بشكل صحيح`, 'success');
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(content => {
                    // فحص المحتوى للتأكد من وجود العناصر المطلوبة
                    if (filename.includes('interactive-story-maker')) {
                        if (content.includes('start-collaboration')) {
                            console.log('✅ زر التعاون موجود');
                        } else {
                            console.error('❌ زر التعاون مفقود');
                        }
                    }
                    
                    if (filename.includes('collaborative-writing')) {
                        if (content.includes('add-to-story')) {
                            console.log('✅ زر إضافة القصة موجود');
                        } else {
                            console.error('❌ زر إضافة القصة مفقود');
                        }
                    }
                })
                .catch(error => {
                    console.error(`❌ خطأ في تحميل ${filename}: ${error.message}`);
                    showResult('file-results', `خطأ في ${filename}: ${error.message}`, 'error');
                });
        }

        function testLocalStorage() {
            console.log('اختبار التخزين المحلي...');
            
            try {
                // اختبار الكتابة
                const testData = {
                    test: true,
                    timestamp: new Date().toISOString(),
                    data: 'بيانات تجريبية'
                };
                
                localStorage.setItem('debugTest', JSON.stringify(testData));
                console.log('✅ تم حفظ البيانات التجريبية');
                
                // اختبار القراءة
                const retrieved = localStorage.getItem('debugTest');
                if (retrieved) {
                    const parsed = JSON.parse(retrieved);
                    console.log('✅ تم استرداد البيانات:', parsed);
                    showResult('js-results', 'التخزين المحلي يعمل بشكل صحيح', 'success');
                } else {
                    throw new Error('فشل في استرداد البيانات');
                }
                
                // تنظيف
                localStorage.removeItem('debugTest');
                console.log('✅ تم تنظيف البيانات التجريبية');
                
            } catch (error) {
                console.error('❌ خطأ في التخزين المحلي:', error.message);
                showResult('js-results', `خطأ في التخزين المحلي: ${error.message}`, 'error');
            }
        }

        function testDataCollection() {
            console.log('اختبار جمع البيانات...');
            
            try {
                // محاكاة جمع البيانات
                const mockData = {
                    textInputs: document.querySelectorAll('input[type="text"], textarea').length,
                    checkboxes: document.querySelectorAll('input[type="checkbox"]').length,
                    radioButtons: document.querySelectorAll('input[type="radio"]').length,
                    selects: document.querySelectorAll('select').length
                };
                
                console.log('📊 عناصر الإدخال الموجودة:', mockData);
                
                if (mockData.textInputs > 0 || mockData.checkboxes > 0 || mockData.radioButtons > 0) {
                    showResult('js-results', `تم العثور على ${Object.values(mockData).reduce((a,b) => a+b, 0)} عنصر إدخال`, 'success');
                } else {
                    showResult('js-results', 'لم يتم العثور على عناصر إدخال في هذه الصفحة', 'warning');
                }
                
            } catch (error) {
                console.error('❌ خطأ في جمع البيانات:', error.message);
                showResult('js-results', `خطأ في جمع البيانات: ${error.message}`, 'error');
            }
        }

        function testStoryGeneration() {
            console.log('اختبار توليد القصص...');
            
            try {
                // محاكاة توليد قصة
                const sampleInput = 'طفل يحب الحيوانات';
                const enhanced = enhanceStoryText(sampleInput);
                
                console.log('📖 النص الأصلي:', sampleInput);
                console.log('✨ النص المحسن:', enhanced);
                
                showResult('js-results', `تم توليد قصة محسنة بنجاح`, 'success');
                
            } catch (error) {
                console.error('❌ خطأ في توليد القصة:', error.message);
                showResult('js-results', `خطأ في توليد القصة: ${error.message}`, 'error');
            }
        }

        function enhanceStoryText(input) {
            let enhanced = input;
            
            // إضافة افتتاحية
            if (!enhanced.startsWith('كان يا ما كان')) {
                enhanced = `كان يا ما كان، ${enhanced}`;
            }
            
            // إضافة توجيهات صوتية
            enhanced += '\n\n*(بصوت مرح ومتحمس)*';
            
            return enhanced;
        }

        function testNavigation() {
            console.log('اختبار التنقل...');
            
            const pages = [
                'index.html',
                'interactive-story-maker.html',
                'collaborative-writing.html',
                'advanced-customization.html'
            ];
            
            let successCount = 0;
            let totalCount = pages.length;
            
            pages.forEach(page => {
                fetch(page, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            console.log(`✅ ${page} متاح`);
                            successCount++;
                        } else {
                            console.error(`❌ ${page} غير متاح (${response.status})`);
                        }
                        
                        if (successCount === totalCount) {
                            showResult('nav-results', 'جميع الصفحات متاحة', 'success');
                        } else if (successCount > 0) {
                            showResult('nav-results', `${successCount}/${totalCount} صفحات متاحة`, 'warning');
                        }
                    })
                    .catch(error => {
                        console.error(`❌ خطأ في الوصول لـ ${page}:`, error.message);
                    });
            });
        }

        function simulateCollaboration() {
            console.log('محاكاة عملية التعاون...');
            
            try {
                // محاكاة بيانات التعاون
                const collaborativeData = {
                    mode: 'collaborative',
                    timestamp: new Date().toISOString(),
                    userInputs: {
                        childName: 'أحمد التجريبي',
                        childAge: '7',
                        preferences: {
                            storyTone: 'exciting',
                            storyLength: 'medium'
                        }
                    },
                    settings: {
                        language: 'arabic',
                        format: 'interactive'
                    }
                };
                
                // حفظ البيانات
                localStorage.setItem('collaborativeStoryData', JSON.stringify(collaborativeData));
                console.log('✅ تم حفظ بيانات التعاون');
                
                // محاكاة الانتقال
                console.log('🔄 محاكاة الانتقال للكتابة التعاونية...');
                
                showResult('nav-results', 'تم محاكاة عملية التعاون بنجاح', 'success');
                
            } catch (error) {
                console.error('❌ خطأ في محاكاة التعاون:', error.message);
                showResult('nav-results', `خطأ في محاكاة التعاون: ${error.message}`, 'error');
            }
        }

        function fixCommonIssues() {
            console.log('إصلاح المشاكل الشائعة...');
            
            try {
                // إصلاح مشاكل التخزين المحلي
                if (typeof Storage !== "undefined") {
                    console.log('✅ التخزين المحلي مدعوم');
                } else {
                    console.error('❌ التخزين المحلي غير مدعوم');
                }
                
                // إصلاح مشاكل JavaScript
                if (typeof fetch !== "undefined") {
                    console.log('✅ Fetch API مدعوم');
                } else {
                    console.error('❌ Fetch API غير مدعوم');
                }
                
                // تنظيف البيانات التالفة
                try {
                    const data = localStorage.getItem('collaborativeStoryData');
                    if (data) {
                        JSON.parse(data);
                        console.log('✅ بيانات التعاون صحيحة');
                    }
                } catch (e) {
                    console.warn('⚠️ بيانات التعاون تالفة، سيتم مسحها');
                    localStorage.removeItem('collaborativeStoryData');
                }
                
                showResult('fix-results', 'تم فحص وإصلاح المشاكل الشائعة', 'success');
                
            } catch (error) {
                console.error('❌ خطأ في الإصلاح:', error.message);
                showResult('fix-results', `خطأ في الإصلاح: ${error.message}`, 'error');
            }
        }

        function resetAllData() {
            console.log('إعادة تعيين جميع البيانات...');
            
            try {
                // مسح جميع البيانات المحفوظة
                const keys = Object.keys(localStorage);
                keys.forEach(key => {
                    if (key.includes('story') || key.includes('collaborative') || key.includes('preset')) {
                        localStorage.removeItem(key);
                        console.log(`🗑️ تم مسح: ${key}`);
                    }
                });
                
                showResult('fix-results', 'تم إعادة تعيين جميع البيانات', 'success');
                
            } catch (error) {
                console.error('❌ خطأ في إعادة التعيين:', error.message);
                showResult('fix-results', `خطأ في إعادة التعيين: ${error.message}`, 'error');
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            console.log('🚀 بدء اختبار النظام...');
            setTimeout(() => {
                testLocalStorage();
                testDataCollection();
            }, 1000);
        });
    </script>
</body>
</html>
