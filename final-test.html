<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار النهائي - ركن الأطفال</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            margin: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .success {
            background: #f0fff4;
            border-left: 4px solid #38a169;
            color: #2f855a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .error {
            background: #fff5f5;
            border-left: 4px solid #e53e3e;
            color: #c53030;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .warning {
            background: #fffbeb;
            border-left: 4px solid #d69e2e;
            color: #b7791f;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .demo-form {
            background: #f7fafc;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .demo-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            margin: 5px 0;
        }
        .demo-input:focus {
            border-color: #667eea;
            outline: none;
        }
    </style>
</head>
<body>
    <div class="container mx-auto max-w-6xl">
        <h1 class="text-4xl font-bold text-white text-center mb-8">
            🎯 الاختبار النهائي الشامل
        </h1>

        <!-- Test Form -->
        <div class="test-card">
            <h2 class="text-2xl font-bold mb-4">📝 نموذج اختبار شامل</h2>
            
            <div class="demo-form">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block mb-2 font-semibold">اسم الطفل:</label>
                        <input type="text" id="child-name" class="demo-input" placeholder="أحمد" value="أحمد التجريبي">
                    </div>
                    <div>
                        <label class="block mb-2 font-semibold">العمر:</label>
                        <input type="number" id="child-age" class="demo-input" placeholder="7" value="7">
                    </div>
                    <div>
                        <label class="block mb-2 font-semibold">النوع:</label>
                        <select id="child-gender" class="demo-input">
                            <option value="boy" selected>ولد</option>
                            <option value="girl">بنت</option>
                        </select>
                    </div>
                    <div>
                        <label class="block mb-2 font-semibold">النبرة:</label>
                        <select id="story-tone" class="demo-input">
                            <option value="gentle">هادئ ولطيف</option>
                            <option value="exciting" selected>مثير ومشوق</option>
                            <option value="educational">تعليمي</option>
                            <option value="funny">مرح</option>
                        </select>
                    </div>
                </div>

                <div class="mt-4">
                    <label class="block mb-2 font-semibold">عناصر القصة:</label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="elements" value="animals" checked class="ml-2">
                            حيوانات
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="elements" value="adventure" checked class="ml-2">
                            مغامرة
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="elements" value="magic" class="ml-2">
                            سحر
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="elements" value="friendship" checked class="ml-2">
                            صداقة
                        </label>
                    </div>
                </div>

                <div class="mt-4">
                    <label class="block mb-2 font-semibold">فكرة القصة:</label>
                    <textarea id="story-idea" class="demo-input" rows="3" placeholder="اكتب فكرة القصة هنا...">طفل يحب الحيوانات يجد قطة صغيرة ضائعة في الحديقة ويساعدها في العثور على أمها</textarea>
                </div>
            </div>

            <div class="flex flex-wrap gap-3 mt-4">
                <button class="test-btn" onclick="testDataCollection()">
                    <i class="fas fa-database ml-2"></i>
                    اختبار جمع البيانات
                </button>
                <button class="test-btn" onclick="testStoryGeneration()">
                    <i class="fas fa-magic ml-2"></i>
                    اختبار توليد القصة
                </button>
                <button class="test-btn" onclick="testCollaborativeFlow()">
                    <i class="fas fa-rocket ml-2"></i>
                    اختبار التعاون الكامل
                </button>
            </div>
            
            <div id="test-results"></div>
        </div>

        <!-- Navigation Test -->
        <div class="test-card">
            <h2 class="text-2xl font-bold mb-4">🔗 اختبار التنقل</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                <button class="test-btn" onclick="openPage('index.html')">
                    <i class="fas fa-home ml-2"></i>
                    الصفحة الرئيسية
                </button>
                <button class="test-btn" onclick="openPage('interactive-story-maker.html')">
                    <i class="fas fa-magic ml-2"></i>
                    صانع القصص
                </button>
                <button class="test-btn" onclick="openPage('collaborative-writing.html')">
                    <i class="fas fa-edit ml-2"></i>
                    الكتابة التعاونية
                </button>
                <button class="test-btn" onclick="openPage('advanced-customization.html')">
                    <i class="fas fa-cog ml-2"></i>
                    التخصيص المتقدم
                </button>
            </div>
            
            <div id="navigation-results"></div>
        </div>

        <!-- Story Output -->
        <div class="test-card">
            <h2 class="text-2xl font-bold mb-4">📖 نتيجة القصة المولدة</h2>
            <div id="story-output" class="bg-gray-50 p-4 rounded-lg min-h-32">
                <p class="text-gray-500 text-center">ستظهر القصة المولدة هنا...</p>
            </div>
        </div>

        <!-- System Status -->
        <div class="test-card">
            <h2 class="text-2xl font-bold mb-4">⚙️ حالة النظام</h2>
            <div id="system-status"></div>
            <button class="test-btn" onclick="checkSystemStatus()">
                <i class="fas fa-heartbeat ml-2"></i>
                فحص حالة النظام
            </button>
        </div>
    </div>

    <script src="fix-errors.js"></script>
    <script>
        function showResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = type;
            result.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'exclamation-triangle'} ml-2"></i>
                ${message}
            `;
            container.appendChild(result);
        }

        function testDataCollection() {
            console.log('🧪 اختبار جمع البيانات...');
            
            try {
                // استخدام الدالة المصححة من fix-errors.js
                const data = window.fixErrors ? 
                    window.fixErrors.collectUserInputsFixed() : 
                    collectUserInputsLocal();
                
                if (data && data['child-name']) {
                    showResult('test-results', 
                        `✅ تم جمع البيانات بنجاح:<br>
                        الاسم: ${data['child-name']}<br>
                        العمر: ${data['child-age']}<br>
                        النبرة: ${data['story-tone']}<br>
                        العناصر: ${data.storyElements ? data.storyElements.length : 0} عنصر`, 
                        'success'
                    );
                } else {
                    showResult('test-results', 'فشل في جمع البيانات', 'error');
                }
            } catch (error) {
                showResult('test-results', `خطأ: ${error.message}`, 'error');
            }
        }

        function collectUserInputsLocal() {
            const userInputs = {};
            
            // جمع المدخلات النصية
            document.querySelectorAll('input[type="text"], input[type="number"], textarea, select').forEach(input => {
                if (input.id) {
                    userInputs[input.id] = input.value || '';
                }
            });
            
            // جمع العناصر المحددة
            const selectedElements = [];
            document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                selectedElements.push({
                    type: checkbox.name || 'element',
                    value: checkbox.value,
                    label: checkbox.value
                });
            });
            
            userInputs.storyElements = selectedElements;
            userInputs.preferences = {
                storyTone: userInputs['story-tone'] || 'gentle',
                storyLength: 'medium',
                language: 'arabic'
            };
            
            return userInputs;
        }

        function testStoryGeneration() {
            console.log('🧪 اختبار توليد القصة...');
            
            try {
                const userInputs = collectUserInputsLocal();
                const storyIdea = userInputs['story-idea'] || 'طفل يحب الحيوانات';
                
                // استخدام الدالة المصححة من fix-errors.js
                const enhancedStory = window.fixErrors ? 
                    window.fixErrors.enhanceUserInputFixed(storyIdea, userInputs) : 
                    enhanceStoryLocal(storyIdea, userInputs);
                
                // عرض القصة
                document.getElementById('story-output').innerHTML = `
                    <div class="bg-white p-4 rounded-lg border-r-4 border-blue-500">
                        <h3 class="font-bold mb-2">القصة المولدة:</h3>
                        <div class="text-lg leading-relaxed">${enhancedStory.replace(/\n/g, '<br>')}</div>
                    </div>
                `;
                
                showResult('test-results', 'تم توليد القصة بنجاح!', 'success');
                
            } catch (error) {
                showResult('test-results', `خطأ في توليد القصة: ${error.message}`, 'error');
            }
        }

        function enhanceStoryLocal(input, settings) {
            let enhanced = input;
            
            // إضافة افتتاحية عربية
            if (!enhanced.startsWith('كان يا ما كان')) {
                enhanced = `كان يا ما كان، ${enhanced}`;
            }
            
            // تطبيق النبرة
            const tone = settings.preferences.storyTone;
            if (tone === 'exciting') {
                enhanced += '\n\nوفجأة، بدأت مغامرة مثيرة ومشوقة!';
            } else if (tone === 'gentle') {
                enhanced += '\n\nوفي جو من السكينة والهدوء...';
            }
            
            // استبدال الأسماء
            if (settings['child-name']) {
                enhanced = enhanced.replace(/الطفل|البطل/g, settings['child-name']);
            }
            
            // إضافة توجيهات صوتية
            enhanced += '\n\n*(بصوت مرح ومتحمس)*';
            
            return enhanced;
        }

        function testCollaborativeFlow() {
            console.log('🧪 اختبار التعاون الكامل...');
            
            try {
                const userInputs = collectUserInputsLocal();
                
                // إنشاء بيانات التعاون
                const collaborativeData = {
                    mode: 'collaborative',
                    timestamp: new Date().toISOString(),
                    userInputs: userInputs,
                    settings: {
                        language: 'arabic',
                        format: 'interactive'
                    }
                };
                
                // حفظ البيانات
                localStorage.setItem('collaborativeStoryData', JSON.stringify(collaborativeData));
                
                showResult('test-results', 
                    'تم حفظ بيانات التعاون بنجاح! يمكنك الآن فتح صفحة الكتابة التعاونية.', 
                    'success'
                );
                
            } catch (error) {
                showResult('test-results', `خطأ في التعاون: ${error.message}`, 'error');
            }
        }

        function openPage(url) {
            try {
                window.open(url, '_blank');
                showResult('navigation-results', `تم فتح ${url} بنجاح`, 'success');
            } catch (error) {
                showResult('navigation-results', `خطأ في فتح ${url}: ${error.message}`, 'error');
            }
        }

        function checkSystemStatus() {
            const status = document.getElementById('system-status');
            status.innerHTML = '';
            
            // فحص التخزين المحلي
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                showResult('system-status', 'التخزين المحلي: يعمل بشكل صحيح', 'success');
            } catch (e) {
                showResult('system-status', 'التخزين المحلي: لا يعمل', 'error');
            }
            
            // فحص JavaScript
            try {
                if (typeof window.fixErrors !== 'undefined') {
                    showResult('system-status', 'ملف الإصلاحات: محمل بنجاح', 'success');
                } else {
                    showResult('system-status', 'ملف الإصلاحات: غير محمل', 'warning');
                }
            } catch (e) {
                showResult('system-status', 'JavaScript: خطأ في التحميل', 'error');
            }
            
            // فحص DOM
            const requiredElements = ['child-name', 'child-age', 'story-tone'];
            let missingElements = [];
            
            requiredElements.forEach(id => {
                if (!document.getElementById(id)) {
                    missingElements.push(id);
                }
            });
            
            if (missingElements.length === 0) {
                showResult('system-status', 'عناصر DOM: جميع العناصر موجودة', 'success');
            } else {
                showResult('system-status', `عناصر DOM: عناصر مفقودة: ${missingElements.join(', ')}`, 'warning');
            }
        }

        // تشغيل فحص تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            console.log('🚀 تم تحميل صفحة الاختبار النهائي');
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
