class InteractiveStoryMaker {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 5;
        this.storyData = {
            childAge: '',
            childGender: '',
            childName: '',
            interests: [],
            storyStyle: '',
            moralLesson: '',
            additionalDetails: '',
            userPreferences: {}
        };

        // تهيئة المكتبات المحسنة
        this.storyResources = new StoryResources();
        this.characterLibrary = new CharacterEnvironmentLibrary();
        this.educationalSystem = new EducationalLessonsSystem();
        
        this.collaborationMessages = {
            welcome: "مرحباً! أنا هنا لمساعدتكم في إنشاء قصة رائعة لطفلكم. دعونا نبدأ بالتعرف على طفلكم الحبيب! 😊",
            ageInput: "ممتاز! الآن أخبروني عن عمر طفلكم. هذا سيساعدني في اختيار المفردات والأحداث المناسبة.",
            genderInput: "رائع! معرفة جنس الطفل مهم جداً لضمان الدقة النحوية في اللغة العربية. سأحرص على استخدام الضمائر والصفات الصحيحة.",
            nameInput: "اسم جميل! سأجعل هذا الاسم محور القصة وأتأكد من استخدامه بالطريقة النحوية الصحيحة.",
            interestsInput: "هذه اهتمامات رائعة! سأدمجها في القصة بطريقة طبيعية ومثيرة.",
            styleInput: "اختيار ممتاز للأسلوب! سأحافظ على هذا النمط طوال القصة.",
            lessonInput: "درس قيم جداً! سأدمجه في القصة بطريقة طبيعية وغير مباشرة.",
            detailsInput: "تفاصيل رائعة! سأدمجها بعناية في نسيج القصة."
        };
        
        this.aiSuggestions = {
            age: [
                "للأطفال الصغار (3-5 سنوات): سأستخدم كلمات بسيطة وجمل قصيرة",
                "للأطفال المتوسطين (6-8 سنوات): سأضيف مفردات أكثر تنوعاً",
                "للأطفال الأكبر (9-12 سنة): سأستخدم تراكيب لغوية أكثر تعقيداً"
            ],
            gender: [
                "سأحرص على مطابقة جميع الضمائر والصفات مع جنس الطفل",
                "سأستخدم الأفعال المناسبة (هو يلعب / هي تلعب)",
                "سأراعي التذكير والتأنيث في جميع أجزاء القصة"
            ],
            interests: [
                "سأجعل اهتمامات الطفل جزءاً أساسياً من شخصية البطل",
                "سأدمج هذه الاهتمامات في الأحداث والمغامرات",
                "سأستخدم هذه الاهتمامات لتطوير الحبكة"
            ]
        };
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateProgress();
    }

    bindEvents() {
        // Start collaboration button
        document.getElementById('start-collaboration').addEventListener('click', () => {
            this.startCollaborativeStory();
        });

        // Back to home button
        document.getElementById('back-to-home').addEventListener('click', () => {
            this.goToHomePage();
        });

        // Navigation buttons
        document.getElementById('next-btn').addEventListener('click', () => {
            this.nextStep();
        });

        document.getElementById('prev-btn').addEventListener('click', () => {
            this.prevStep();
        });
    }

    nextStep() {
        if (this.currentStep < this.totalSteps) {
            this.hideCurrentStep();
            this.currentStep++;
            this.showCurrentStep();
            this.updateProgress();
            this.updateStepIndicators();
            
            if (this.currentStep === 2) {
                this.initializeCollaborationWorkspace();
            }
        }
    }

    prevStep() {
        if (this.currentStep > 1) {
            this.hideCurrentStep();
            this.currentStep--;
            this.showCurrentStep();
            this.updateProgress();
            this.updateStepIndicators();
        }
    }

    hideCurrentStep() {
        const currentStepElement = document.getElementById(`step-${this.currentStep}`);
        if (currentStepElement) {
            currentStepElement.classList.add('hidden');
        }
    }

    showCurrentStep() {
        const currentStepElement = document.getElementById(`step-${this.currentStep}`);
        if (currentStepElement) {
            currentStepElement.classList.remove('hidden');
        }
        
        // Show/hide navigation buttons
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        
        if (this.currentStep > 1) {
            prevBtn.classList.remove('hidden');
        } else {
            prevBtn.classList.add('hidden');
        }
        
        if (this.currentStep < this.totalSteps) {
            nextBtn.classList.remove('hidden');
        } else {
            nextBtn.classList.add('hidden');
        }
    }

    updateProgress() {
        const progressPercentage = (this.currentStep / this.totalSteps) * 100;
        document.getElementById('progress-fill').style.width = `${progressPercentage}%`;
    }

    updateStepIndicators() {
        for (let i = 1; i <= this.totalSteps; i++) {
            const indicator = document.getElementById(`step-indicator-${i}`);
            const line = document.getElementById(`step-line-${i}`);
            
            if (indicator) {
                indicator.classList.remove('active', 'completed', 'inactive');
                
                if (i < this.currentStep) {
                    indicator.classList.add('completed');
                    indicator.innerHTML = '<i class="fas fa-check"></i>';
                } else if (i === this.currentStep) {
                    indicator.classList.add('active');
                    indicator.innerHTML = i;
                } else {
                    indicator.classList.add('inactive');
                    indicator.innerHTML = i;
                }
            }
            
            if (line) {
                if (i < this.currentStep) {
                    line.classList.add('active');
                } else {
                    line.classList.remove('active');
                }
            }
        }
    }

    initializeCollaborationWorkspace() {
        this.createAgeInputSection();
    }

    createAgeInputSection() {
        const userInputs = document.getElementById('user-inputs');
        const aiSuggestions = document.getElementById('ai-suggestions');
        
        // User input section
        userInputs.innerHTML = `
            <div class="user-input-area">
                <h4 class="text-lg font-bold text-gray-800 mb-3 arabic-text">
                    <i class="fas fa-birthday-cake text-pink-500 ml-2"></i>
                    كم عمر طفلكم؟
                </h4>
                <div class="grid grid-cols-1 gap-3">
                    <button class="age-option bg-gradient-to-r from-pink-400 to-pink-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-age="3-5">
                        3-5 سنوات (طفل صغير) 🧸
                    </button>
                    <button class="age-option bg-gradient-to-r from-blue-400 to-blue-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-age="6-8">
                        6-8 سنوات (طفل متوسط) 🎈
                    </button>
                    <button class="age-option bg-gradient-to-r from-green-400 to-green-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-age="9-12">
                        9-12 سنة (طفل كبير) 🎯
                    </button>
                </div>
            </div>
        `;
        
        // AI suggestions section
        aiSuggestions.innerHTML = `
            <div class="ai-suggestion">
                <h4 class="text-lg font-bold mb-3 arabic-text">
                    <i class="fas fa-lightbulb ml-2"></i>
                    كيف سأساعدكم:
                </h4>
                <p class="arabic-text mb-3">${this.collaborationMessages.ageInput}</p>
                <div class="space-y-2">
                    ${this.aiSuggestions.age.map(suggestion => 
                        `<div class="bg-white bg-opacity-20 p-2 rounded-lg text-sm arabic-text">
                            <i class="fas fa-check-circle text-green-300 ml-1"></i>
                            ${suggestion}
                        </div>`
                    ).join('')}
                </div>
            </div>
        `;
        
        // Bind age selection events
        document.querySelectorAll('.age-option').forEach(button => {
            button.addEventListener('click', (e) => {
                this.selectAge(e.target.dataset.age);
            });
        });
    }

    selectAge(age) {
        this.storyData.childAge = age;
        
        // Update UI to show selection
        document.querySelectorAll('.age-option').forEach(btn => {
            btn.classList.remove('ring-4', 'ring-yellow-300');
        });
        
        event.target.classList.add('ring-4', 'ring-yellow-300');
        
        // Show AI response
        this.showAIResponse(`ممتاز! اخترتم عمر ${age}. سأحرص على استخدام مفردات وتراكيب مناسبة لهذا العمر. الآن دعونا نتعرف على جنس الطفل.`);
        
        // Move to next input after delay
        setTimeout(() => {
            this.createGenderInputSection();
        }, 2000);
    }

    createGenderInputSection() {
        const userInputs = document.getElementById('user-inputs');
        
        userInputs.innerHTML += `
            <div class="user-input-area mt-4">
                <h4 class="text-lg font-bold text-gray-800 mb-3 arabic-text">
                    <i class="fas fa-venus-mars text-purple-500 ml-2"></i>
                    ما جنس طفلكم؟
                </h4>
                <div class="grid grid-cols-1 gap-3">
                    <button class="gender-option bg-gradient-to-r from-blue-400 to-blue-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-gender="boy">
                        ولد 👦
                    </button>
                    <button class="gender-option bg-gradient-to-r from-pink-400 to-pink-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-gender="girl">
                        بنت 👧
                    </button>
                    <button class="gender-option bg-gradient-to-r from-purple-400 to-purple-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-gender="neutral">
                        أفضل عدم التحديد 🧒
                    </button>
                </div>
            </div>
        `;
        
        // Update AI suggestions
        const aiSuggestions = document.getElementById('ai-suggestions');
        aiSuggestions.innerHTML += `
            <div class="ai-suggestion mt-4">
                <h4 class="text-lg font-bold mb-3 arabic-text">
                    <i class="fas fa-language ml-2"></i>
                    أهمية الدقة النحوية:
                </h4>
                <p class="arabic-text mb-3">${this.collaborationMessages.genderInput}</p>
                <div class="space-y-2">
                    ${this.aiSuggestions.gender.map(suggestion => 
                        `<div class="bg-white bg-opacity-20 p-2 rounded-lg text-sm arabic-text">
                            <i class="fas fa-check-circle text-green-300 ml-1"></i>
                            ${suggestion}
                        </div>`
                    ).join('')}
                </div>
            </div>
        `;
        
        // Bind gender selection events
        document.querySelectorAll('.gender-option').forEach(button => {
            button.addEventListener('click', (e) => {
                this.selectGender(e.target.dataset.gender);
            });
        });
    }

    selectGender(gender) {
        this.storyData.childGender = gender;
        
        // Update UI to show selection
        document.querySelectorAll('.gender-option').forEach(btn => {
            btn.classList.remove('ring-4', 'ring-yellow-300');
        });
        
        event.target.classList.add('ring-4', 'ring-yellow-300');
        
        const genderText = gender === 'boy' ? 'ولد' : gender === 'girl' ? 'بنت' : 'طفل';
        this.showAIResponse(`رائع! سأحرص على استخدام جميع الضمائر والصفات المناسبة لـ${genderText}. الآن، ما اسم طفلكم الحبيب؟`);
        
        // Move to next input after delay
        setTimeout(() => {
            this.createNameInputSection();
        }, 2000);
    }

    createNameInputSection() {
        const userInputs = document.getElementById('user-inputs');
        
        userInputs.innerHTML += `
            <div class="user-input-area mt-4">
                <h4 class="text-lg font-bold text-gray-800 mb-3 arabic-text">
                    <i class="fas fa-user text-green-500 ml-2"></i>
                    ما اسم طفلكم؟
                </h4>
                <input type="text" id="child-name" placeholder="اكتبوا اسم الطفل هنا..." 
                       class="w-full p-3 border-2 border-gray-300 rounded-lg text-lg arabic-text focus:border-blue-500 focus:outline-none"
                       maxlength="20">
                <div class="text-sm text-gray-600 mt-2 arabic-text">
                    <span id="name-count">0</span>/20 حرف
                </div>
                <button id="confirm-name" class="interactive-button mt-3 arabic-text" disabled>
                    <i class="fas fa-check ml-2"></i>
                    تأكيد الاسم
                </button>
            </div>
        `;
        
        // Bind name input events
        const nameInput = document.getElementById('child-name');
        const confirmBtn = document.getElementById('confirm-name');
        const nameCount = document.getElementById('name-count');
        
        nameInput.addEventListener('input', (e) => {
            const value = e.target.value.trim();
            nameCount.textContent = value.length;
            
            if (value.length > 0) {
                confirmBtn.disabled = false;
                confirmBtn.classList.remove('opacity-50');
            } else {
                confirmBtn.disabled = true;
                confirmBtn.classList.add('opacity-50');
            }
        });
        
        confirmBtn.addEventListener('click', () => {
            const name = nameInput.value.trim();
            if (name) {
                this.selectName(name);
            }
        });
    }

    selectName(name) {
        this.storyData.childName = name;
        
        this.showAIResponse(`${name} - اسم جميل جداً! سأجعل ${name} بطل/بطلة قصتنا وأحرص على استخدام الاسم بالطريقة النحوية الصحيحة. الآن، أخبروني عن اهتمامات ${name}!`);
        
        // Enable next step
        setTimeout(() => {
            document.getElementById('next-btn').classList.remove('hidden');
            document.getElementById('next-btn').textContent = 'متابعة إنشاء القصة ✨';
        }, 2000);
    }

    showAIResponse(message) {
        const aiSuggestions = document.getElementById('ai-suggestions');

        const responseDiv = document.createElement('div');
        responseDiv.className = 'ai-suggestion mt-4';
        responseDiv.innerHTML = `
            <div class="flex items-start">
                <i class="fas fa-robot text-2xl text-blue-300 ml-3 mt-1"></i>
                <div>
                    <h4 class="text-lg font-bold mb-2 arabic-text">مساعدكم الذكي يقول:</h4>
                    <p class="arabic-text">${message}</p>
                </div>
            </div>
        `;

        aiSuggestions.appendChild(responseDiv);

        // Scroll to show the new response
        responseDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    createInterestsInputSection() {
        const userInputs = document.getElementById('user-inputs');

        userInputs.innerHTML = `
            <div class="user-input-area">
                <h4 class="text-lg font-bold text-gray-800 mb-3 arabic-text">
                    <i class="fas fa-star text-yellow-500 ml-2"></i>
                    ما هي اهتمامات ${this.storyData.childName}؟
                </h4>
                <p class="text-sm text-gray-600 mb-4 arabic-text">اختاروا حتى 4 اهتمامات</p>
                <div class="grid grid-cols-2 gap-3">
                    <button class="interest-option bg-gradient-to-r from-green-400 to-green-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-interest="animals">
                        🐾 الحيوانات
                    </button>
                    <button class="interest-option bg-gradient-to-r from-blue-400 to-blue-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-interest="space">
                        🚀 الفضاء
                    </button>
                    <button class="interest-option bg-gradient-to-r from-pink-400 to-pink-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-interest="princesses">
                        👑 الأميرات
                    </button>
                    <button class="interest-option bg-gradient-to-r from-orange-400 to-orange-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-interest="dinosaurs">
                        🦖 الديناصورات
                    </button>
                    <button class="interest-option bg-gradient-to-r from-purple-400 to-purple-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-interest="magic">
                        🪄 السحر
                    </button>
                    <button class="interest-option bg-gradient-to-r from-red-400 to-red-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-interest="cars">
                        🚗 السيارات
                    </button>
                    <button class="interest-option bg-gradient-to-r from-teal-400 to-teal-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-interest="ocean">
                        🌊 البحر
                    </button>
                    <button class="interest-option bg-gradient-to-r from-yellow-400 to-yellow-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-interest="books">
                        📚 الكتب
                    </button>
                </div>
                <div class="mt-4">
                    <p class="text-sm text-gray-600 arabic-text">المختار: <span id="selected-interests">لا شيء</span></p>
                    <button id="confirm-interests" class="interactive-button mt-3 arabic-text" disabled>
                        <i class="fas fa-check ml-2"></i>
                        تأكيد الاهتمامات
                    </button>
                </div>
            </div>
        `;

        // Bind interest selection events
        document.querySelectorAll('.interest-option').forEach(button => {
            button.addEventListener('click', (e) => {
                this.toggleInterest(e.target.dataset.interest, e.target);
            });
        });

        document.getElementById('confirm-interests').addEventListener('click', () => {
            this.confirmInterests();
        });
    }

    toggleInterest(interest, buttonElement) {
        const interestIndex = this.storyData.interests.indexOf(interest);

        if (interestIndex > -1) {
            // Remove interest
            this.storyData.interests.splice(interestIndex, 1);
            buttonElement.classList.remove('ring-4', 'ring-yellow-300');
        } else {
            // Add interest (max 4)
            if (this.storyData.interests.length < 4) {
                this.storyData.interests.push(interest);
                buttonElement.classList.add('ring-4', 'ring-yellow-300');
            } else {
                this.showAIResponse('يمكنكم اختيار 4 اهتمامات كحد أقصى. يرجى إلغاء اختيار أحد الاهتمامات أولاً.');
                return;
            }
        }

        this.updateInterestsDisplay();
    }

    updateInterestsDisplay() {
        const selectedElement = document.getElementById('selected-interests');
        const confirmBtn = document.getElementById('confirm-interests');

        if (this.storyData.interests.length === 0) {
            selectedElement.textContent = 'لا شيء';
            confirmBtn.disabled = true;
            confirmBtn.classList.add('opacity-50');
        } else {
            const interestNames = {
                'animals': 'الحيوانات',
                'space': 'الفضاء',
                'princesses': 'الأميرات',
                'dinosaurs': 'الديناصورات',
                'magic': 'السحر',
                'cars': 'السيارات',
                'ocean': 'البحر',
                'books': 'الكتب'
            };

            const selectedNames = this.storyData.interests.map(interest => interestNames[interest]);
            selectedElement.textContent = selectedNames.join('، ');

            confirmBtn.disabled = false;
            confirmBtn.classList.remove('opacity-50');
        }
    }

    confirmInterests() {
        if (this.storyData.interests.length > 0) {
            const interestNames = {
                'animals': 'الحيوانات',
                'space': 'الفضاء',
                'princesses': 'الأميرات',
                'dinosaurs': 'الديناصورات',
                'magic': 'السحر',
                'cars': 'السيارات',
                'ocean': 'البحر',
                'books': 'الكتب'
            };

            const selectedNames = this.storyData.interests.map(interest => interestNames[interest]);
            this.showAIResponse(`اختيارات رائعة! سأدمج ${selectedNames.join(' و')} في قصة ${this.storyData.childName} بطريقة مثيرة وممتعة. الآن دعونا نختار أسلوب القصة.`);

            setTimeout(() => {
                this.createStyleInputSection();
            }, 2000);
        }
    }

    createStyleInputSection() {
        const userInputs = document.getElementById('user-inputs');

        userInputs.innerHTML += `
            <div class="user-input-area mt-4">
                <h4 class="text-lg font-bold text-gray-800 mb-3 arabic-text">
                    <i class="fas fa-palette text-purple-500 ml-2"></i>
                    ما أسلوب القصة المفضل؟
                </h4>
                <div class="grid grid-cols-2 gap-3">
                    <button class="style-option bg-gradient-to-r from-yellow-400 to-yellow-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-style="funny">
                        😄 مضحك ومرح
                    </button>
                    <button class="style-option bg-gradient-to-r from-blue-400 to-blue-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-style="gentle">
                        🌸 هادئ ولطيف
                    </button>
                    <button class="style-option bg-gradient-to-r from-red-400 to-red-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-style="exciting">
                        ⚡ مثير ومغامر
                    </button>
                    <button class="style-option bg-gradient-to-r from-purple-400 to-purple-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-style="magical">
                        ✨ سحري وخيالي
                    </button>
                    <button class="style-option bg-gradient-to-r from-green-400 to-green-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-style="educational">
                        🎓 تعليمي ومفيد
                    </button>
                    <button class="style-option bg-gradient-to-r from-pink-400 to-pink-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-style="emotional">
                        💝 عاطفي ومؤثر
                    </button>
                </div>
            </div>
        `;

        // Bind style selection events
        document.querySelectorAll('.style-option').forEach(button => {
            button.addEventListener('click', (e) => {
                this.selectStyle(e.target.dataset.style, e.target);
            });
        });
    }

    selectStyle(style, buttonElement) {
        this.storyData.storyStyle = style;

        // Update UI to show selection
        document.querySelectorAll('.style-option').forEach(btn => {
            btn.classList.remove('ring-4', 'ring-yellow-300');
        });

        buttonElement.classList.add('ring-4', 'ring-yellow-300');

        const styleNames = {
            'funny': 'مضحك ومرح',
            'gentle': 'هادئ ولطيف',
            'exciting': 'مثير ومغامر',
            'magical': 'سحري وخيالي',
            'educational': 'تعليمي ومفيد',
            'emotional': 'عاطفي ومؤثر'
        };

        this.showAIResponse(`ممتاز! سأكتب القصة بأسلوب ${styleNames[style]}. الآن، ما الدرس الأخلاقي الذي تريدون أن يتعلمه ${this.storyData.childName}؟`);

        setTimeout(() => {
            this.createLessonInputSection();
        }, 2000);
    }

    createLessonInputSection() {
        const userInputs = document.getElementById('user-inputs');

        userInputs.innerHTML += `
            <div class="user-input-area mt-4">
                <h4 class="text-lg font-bold text-gray-800 mb-3 arabic-text">
                    <i class="fas fa-lightbulb text-yellow-500 ml-2"></i>
                    ما الدرس الأخلاقي المطلوب؟
                </h4>
                <div class="grid grid-cols-2 gap-3">
                    <button class="lesson-option bg-gradient-to-r from-green-400 to-green-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-lesson="kindness">
                        💚 اللطف والرحمة
                    </button>
                    <button class="lesson-option bg-gradient-to-r from-blue-400 to-blue-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-lesson="sharing">
                        🤝 المشاركة
                    </button>
                    <button class="lesson-option bg-gradient-to-r from-red-400 to-red-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-lesson="courage">
                        🦁 الشجاعة
                    </button>
                    <button class="lesson-option bg-gradient-to-r from-purple-400 to-purple-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-lesson="honesty">
                        🤲 الصدق
                    </button>
                    <button class="lesson-option bg-gradient-to-r from-yellow-400 to-yellow-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-lesson="perseverance">
                        💪 المثابرة
                    </button>
                    <button class="lesson-option bg-gradient-to-r from-pink-400 to-pink-600 text-white p-3 rounded-lg font-medium arabic-text hover:shadow-lg transition-all" data-lesson="friendship">
                        👫 الصداقة
                    </button>
                </div>
            </div>
        `;

        // Bind lesson selection events
        document.querySelectorAll('.lesson-option').forEach(button => {
            button.addEventListener('click', (e) => {
                this.selectLesson(e.target.dataset.lesson, e.target);
            });
        });
    }

    selectLesson(lesson, buttonElement) {
        this.storyData.moralLesson = lesson;

        // Update UI to show selection
        document.querySelectorAll('.lesson-option').forEach(btn => {
            btn.classList.remove('ring-4', 'ring-yellow-300');
        });

        buttonElement.classList.add('ring-4', 'ring-yellow-300');

        const lessonNames = {
            'kindness': 'اللطف والرحمة',
            'sharing': 'المشاركة',
            'courage': 'الشجاعة',
            'honesty': 'الصدق',
            'perseverance': 'المثابرة',
            'friendship': 'الصداقة'
        };

        this.showAIResponse(`درس قيم جداً! سأدمج درس ${lessonNames[lesson]} في القصة بطريقة طبيعية وغير مباشرة. أخيراً، هل لديكم أي تفاصيل إضافية خاصة تريدون إضافتها؟`);

        setTimeout(() => {
            this.createAdditionalDetailsSection();
        }, 2000);
    }

    createAdditionalDetailsSection() {
        const userInputs = document.getElementById('user-inputs');

        userInputs.innerHTML += `
            <div class="user-input-area mt-4">
                <h4 class="text-lg font-bold text-gray-800 mb-3 arabic-text">
                    <i class="fas fa-plus-circle text-green-500 ml-2"></i>
                    تفاصيل إضافية (اختياري)
                </h4>
                <p class="text-sm text-gray-600 mb-3 arabic-text">
                    أضيفوا أي تفاصيل خاصة تريدون دمجها في القصة
                </p>
                <textarea id="additional-details"
                          placeholder="مثال: أريد أن يكون البطل طائراً يحب الغناء، أو أن تحدث القصة في حديقة جدتي..."
                          class="w-full p-3 border-2 border-gray-300 rounded-lg text-lg arabic-text focus:border-blue-500 focus:outline-none h-24 resize-none"
                          maxlength="200"></textarea>
                <div class="text-sm text-gray-600 mt-2 arabic-text">
                    <span id="details-count">0</span>/200 حرف
                </div>
                <div class="flex gap-3 mt-4">
                    <button id="skip-details" class="interactive-button bg-gray-500 arabic-text">
                        <i class="fas fa-forward ml-2"></i>
                        تخطي
                    </button>
                    <button id="confirm-details" class="interactive-button arabic-text">
                        <i class="fas fa-check ml-2"></i>
                        إضافة التفاصيل
                    </button>
                </div>
            </div>
        `;

        // Bind events
        const detailsTextarea = document.getElementById('additional-details');
        const detailsCount = document.getElementById('details-count');
        const skipBtn = document.getElementById('skip-details');
        const confirmBtn = document.getElementById('confirm-details');

        detailsTextarea.addEventListener('input', (e) => {
            detailsCount.textContent = e.target.value.length;
        });

        skipBtn.addEventListener('click', () => {
            this.finalizeStoryData();
        });

        confirmBtn.addEventListener('click', () => {
            this.storyData.additionalDetails = detailsTextarea.value.trim();
            this.finalizeStoryData();
        });
    }

    finalizeStoryData() {
        // Save story data to localStorage
        localStorage.setItem('interactiveStoryData', JSON.stringify(this.storyData));

        this.showAIResponse(`ممتاز! لدي الآن جميع المعلومات اللازمة لإنشاء قصة رائعة لـ${this.storyData.childName}. سأحرص على الدقة اللغوية المطلقة والتذكير والتأنيث الصحيح. دعونا ننشئ القصة الآن! ✨`);

        // Show generate story button
        setTimeout(() => {
            const userInputs = document.getElementById('user-inputs');
            userInputs.innerHTML += `
                <div class="text-center mt-6">
                    <button id="generate-story" class="interactive-button text-xl px-8 py-4 arabic-text bg-gradient-to-r from-green-500 to-blue-500">
                        <i class="fas fa-magic ml-3"></i>
                        إنشاء القصة الآن! 🎭
                    </button>
                </div>
            `;

            document.getElementById('generate-story').addEventListener('click', () => {
                this.generateStory();
            });
        }, 2000);
    }

    generateStory() {
        // جمع بيانات القصة المحسنة
        const enhancedStoryData = this.prepareEnhancedStoryData();

        // حفظ البيانات في localStorage
        localStorage.setItem('enhancedStoryData', JSON.stringify(enhancedStoryData));

        // الانتقال إلى صفحة النتيجة
        window.location.href = 'story-result.html';
    }

    prepareEnhancedStoryData() {
        // الحصول على شخصية مناسبة
        const selectedCharacter = this.characterLibrary.getRandomCharacter('heroes');

        // الحصول على بيئة مناسبة
        const selectedEnvironment = this.characterLibrary.getRandomEnvironment();

        // الحصول على درس تعليمي مناسب للعمر
        const selectedLesson = this.educationalSystem.getLessonByAge(
            parseInt(this.storyData.childAge) || 6
        );

        // الحصول على موارد القصة
        const storyOpening = this.storyResources.getRandomOpening();
        const plotTemplate = this.storyResources.getRandomPlotTemplate();

        // تحضير البيانات المحسنة
        return {
            ...this.storyData,
            enhancedElements: {
                character: selectedCharacter,
                environment: selectedEnvironment,
                lesson: selectedLesson,
                opening: storyOpening,
                plotTemplate: plotTemplate,
                storyTone: this.storyResources.getStoryTones().gentle,
                complexity: this.determineComplexity(),
                interactiveElements: this.getRecommendedInteractiveElements()
            },
            generationTimestamp: new Date().toISOString()
        };
    }

    determineComplexity() {
        const age = parseInt(this.storyData.childAge) || 6;
        if (age <= 5) return 'simple';
        if (age <= 8) return 'medium';
        return 'advanced';
    }

    getRecommendedInteractiveElements() {
        const age = parseInt(this.storyData.childAge) || 6;
        const baseElements = ['choices'];

        if (age >= 6) {
            baseElements.push('questions');
        }

        if (age >= 8) {
            baseElements.push('sounds', 'actions');
        }

        return baseElements;
    }

    // ===== دوال التنقل والتعاون =====

    startCollaborativeStory() {
        // إنشاء بيانات القصة التعاونية
        const collaborativeData = {
            mode: 'collaborative',
            timestamp: new Date().toISOString(),
            userInputs: this.collectUserInputs(),
            settings: this.getDefaultSettings()
        };

        // حفظ البيانات في localStorage
        localStorage.setItem('collaborativeStoryData', JSON.stringify(collaborativeData));

        // إضافة تأثير انتقال
        this.showTransitionEffect();

        // فتح صفحة الكتابة التعاونية
        setTimeout(() => {
            window.location.href = 'collaborative-writing.html';
        }, 1000);
    }

    goToHomePage() {
        // إضافة تأثير انتقال
        this.showTransitionEffect();

        // العودة للصفحة الرئيسية
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 800);
    }

    collectUserInputs() {
        // جمع المدخلات الحالية من المستخدم
        const userInputs = {};

        // جمع جميع المدخلات النصية
        const textInputs = document.querySelectorAll('input[type="text"], textarea');
        textInputs.forEach(input => {
            if (input.id) {
                userInputs[input.id] = input.value || '';
            }
        });

        // جمع الخيارات المحددة
        const selectedOptions = document.querySelectorAll('input[type="radio"]:checked, input[type="checkbox"]:checked');
        selectedOptions.forEach(option => {
            if (option.name) {
                if (!userInputs[option.name]) {
                    userInputs[option.name] = [];
                }
                if (Array.isArray(userInputs[option.name])) {
                    userInputs[option.name].push(option.value);
                } else {
                    userInputs[option.name] = option.value;
                }
            }
        });

        // جمع عناصر القصة المحددة
        userInputs.storyElements = this.getSelectedElements();
        userInputs.preferences = this.getUserPreferences();
        userInputs.currentStep = this.currentStep;
        userInputs.collectionTime = new Date().toISOString();

        return userInputs;
    }

    getSelectedElements() {
        const elements = [];

        // جمع العناصر المحددة من checkboxes
        document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
            elements.push({
                type: checkbox.name || 'element',
                value: checkbox.value,
                label: checkbox.nextElementSibling?.textContent || checkbox.value,
                id: checkbox.id
            });
        });

        // جمع العناصر المحددة من select elements
        document.querySelectorAll('select').forEach(select => {
            if (select.value) {
                elements.push({
                    type: select.name || select.id || 'selection',
                    value: select.value,
                    label: select.options[select.selectedIndex]?.text || select.value,
                    id: select.id
                });
            }
        });

        return elements;
    }

    getUserPreferences() {
        const preferences = {};

        // جمع تفضيلات طول القصة
        const lengthElement = document.getElementById('story-length') ||
                             document.querySelector('input[name="story-length"]:checked') ||
                             document.querySelector('select[name="length"]');
        if (lengthElement) {
            preferences.storyLength = lengthElement.value || 'medium';
        }

        // جمع تفضيلات نبرة القصة
        const toneElement = document.querySelector('input[name="story-tone"]:checked') ||
                           document.querySelector('select[name="tone"]') ||
                           document.querySelector('[data-tone].selected');
        if (toneElement) {
            preferences.storyTone = toneElement.value || toneElement.dataset.tone || 'gentle';
        }

        // جمع مستوى التفاعل
        const interactiveElement = document.getElementById('interactive-level') ||
                                  document.querySelector('input[name="interactive-level"]:checked');
        if (interactiveElement) {
            preferences.interactiveLevel = interactiveElement.value || 'medium';
        }

        // جمع تفضيلات إضافية
        preferences.language = 'arabic';
        preferences.culturalElements = true;
        preferences.voiceDirections = true;

        return preferences;
    }

    getDefaultSettings() {
        return {
            language: 'arabic',
            format: 'interactive',
            includeVoiceDirections: true,
            includeIllustrationSuggestions: true,
            culturalElements: true,
            educationalContent: true
        };
    }

    showTransitionEffect() {
        // إنشاء تأثير انتقال جميل
        const overlay = document.createElement('div');
        overlay.className = 'transition-overlay';
        overlay.innerHTML = `
            <div class="transition-content">
                <div class="loading-spinner"></div>
                <h3 class="transition-text">جاري التحضير للكتابة التعاونية...</h3>
                <div class="magic-particles"></div>
            </div>
        `;

        // إضافة CSS للتأثير
        const style = document.createElement('style');
        style.textContent = `
            .transition-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                opacity: 0;
                animation: fadeIn 0.5s ease-in-out forwards;
            }

            .transition-content {
                text-align: center;
                color: white;
                animation: slideUp 0.8s ease-out;
            }

            .loading-spinner {
                width: 60px;
                height: 60px;
                border: 4px solid rgba(255,255,255,0.3);
                border-top: 4px solid white;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            }

            .transition-text {
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 20px;
                animation: pulse 2s ease-in-out infinite;
            }

            .magic-particles {
                position: absolute;
                width: 100%;
                height: 100%;
                pointer-events: none;
            }

            .magic-particles::before,
            .magic-particles::after {
                content: '✨';
                position: absolute;
                font-size: 2rem;
                animation: float 3s ease-in-out infinite;
            }

            .magic-particles::before {
                top: 20%;
                left: 20%;
                animation-delay: 0s;
            }

            .magic-particles::after {
                top: 60%;
                right: 20%;
                animation-delay: 1.5s;
            }

            @keyframes fadeIn {
                to { opacity: 1; }
            }

            @keyframes slideUp {
                from { transform: translateY(50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }

            @keyframes spin {
                to { transform: rotate(360deg); }
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.7; }
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px) rotate(0deg); }
                50% { transform: translateY(-20px) rotate(180deg); }
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(overlay);

        // إضافة جزيئات سحرية إضافية
        this.createMagicParticles(overlay.querySelector('.magic-particles'));
    }

    createMagicParticles(container) {
        const particles = ['⭐', '🌟', '✨', '💫', '🎭', '📚', '🎨'];

        for (let i = 0; i < 8; i++) {
            const particle = document.createElement('div');
            particle.textContent = particles[Math.floor(Math.random() * particles.length)];
            particle.style.cssText = `
                position: absolute;
                font-size: 1.5rem;
                top: ${Math.random() * 100}%;
                left: ${Math.random() * 100}%;
                animation: float ${2 + Math.random() * 2}s ease-in-out infinite;
                animation-delay: ${Math.random() * 2}s;
                pointer-events: none;
            `;
            container.appendChild(particle);
        }
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    new InteractiveStoryMaker();
});
