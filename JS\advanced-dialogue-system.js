// 🎭 نظام الحوارات المتقدم والشخصيات الحيوية 🗣️
// Advanced Dialogue System & Dynamic Characters
// Dr.<PERSON>, SUST -BME, @ 2025

class AdvancedDialogueSystem {
    constructor() {
        this.initializeSystem();
    }

    initializeSystem() {
        this.characterPersonalities = this.getCharacterPersonalities();
        this.dialogueStyles = this.getDialogueStyles();
        this.emotionalStates = this.getEmotionalStates();
        this.conversationPatterns = this.getConversationPatterns();
        this.culturalExpressions = this.getCulturalExpressions();
        this.ageAppropriateLanguage = this.getAgeAppropriateLanguage();
    }

    // ===== شخصيات الأطفال المتنوعة =====
    
    getCharacterPersonalities() {
        return {
            curious_child: {
                name_examples: ['سلمى', 'عمر', 'نور', 'يوسف'],
                personality_traits: ['فضولي', 'نشيط', 'يحب الاستطلاع'],
                speech_patterns: {
                    questions: ['ماذا؟', 'لماذا؟', 'كيف؟', 'أين؟'],
                    exclamations: ['واااو!', 'يا الله!', 'انظروا!', 'ما هذا؟'],
                    reactions: ['عجيب!', 'مذهل!', 'لم أر مثل هذا من قبل!']
                },
                voice_characteristics: {
                    tone: 'عالي ومتحمس',
                    speed: 'سريع عند الإثارة',
                    volume: 'مرتفع عند الاكتشاف'
                },
                typical_phrases: [
                    'أريد أن أعرف!',
                    'دعني أرى بنفسي!',
                    'هل يمكنني أن ألمسه؟',
                    'ما الذي سيحدث بعد ذلك؟'
                ]
            },

            shy_child: {
                name_examples: ['ليلى', 'أحمد', 'زينب', 'محمد'],
                personality_traits: ['خجول', 'حساس', 'مراقب'],
                speech_patterns: {
                    hesitations: ['أم...', 'ربما...', 'لست متأكداً...'],
                    soft_expressions: ['بصوت خافت', 'بهدوء', 'بحذر'],
                    seeking_approval: ['هل هذا صحيح؟', 'ماذا تعتقدون؟']
                },
                voice_characteristics: {
                    tone: 'ناعم وهادئ',
                    speed: 'بطيء ومتأني',
                    volume: 'منخفض ومتردد'
                },
                typical_phrases: [
                    'لا أعرف إن كان بإمكاني...',
                    'هل تعتقد أنه آمن؟',
                    'سأحاول... ولكن بحذر',
                    'أشعر بالخوف قليلاً'
                ]
            },

            brave_child: {
                name_examples: ['فارس', 'ملك', 'عبدالله', 'فاطمة'],
                personality_traits: ['شجاع', 'قائد', 'محمي للآخرين'],
                speech_patterns: {
                    confident_statements: ['سأفعل ذلك!', 'لا تقلقوا!', 'أنا هنا!'],
                    encouraging_others: ['تعالوا معي!', 'لا تخافوا!', 'سنتمكن من ذلك!'],
                    taking_charge: ['دعوني أذهب أولاً', 'سأحميكم', 'اتبعوني']
                },
                voice_characteristics: {
                    tone: 'قوي وواثق',
                    speed: 'متوسط ومحكم',
                    volume: 'واضح ومسموع'
                },
                typical_phrases: [
                    'لن أتراجع!',
                    'سأساعدك مهما حدث',
                    'الشجعان لا يتركون أصدقاءهم',
                    'معاً سنتغلب على أي شيء'
                ]
            },

            funny_child: {
                name_examples: ['طارق', 'مريم', 'كريم', 'هدى'],
                personality_traits: ['مرح', 'مضحك', 'محب للمزاح'],
                speech_patterns: {
                    jokes: ['هههههه!', 'هذا مضحك!', 'انظروا إلى هذا!'],
                    playful_sounds: ['لا لا لا!', 'يا هوووو!', 'تيك توك!'],
                    funny_observations: ['يبدو مثل...', 'يذكرني بـ...', 'كأنه...']
                },
                voice_characteristics: {
                    tone: 'مرح ومتقلب',
                    speed: 'متغير حسب المزحة',
                    volume: 'مرتفع عند الضحك'
                },
                typical_phrases: [
                    'هذا يجعلني أضحك!',
                    'دعونا نلعب لعبة!',
                    'أعرف نكتة جميلة!',
                    'الحياة أجمل عندما نضحك'
                ]
            }
        };
    }

    // ===== أساليب الحوار المتطورة =====
    
    getDialogueStyles() {
        return {
            discovery_dialogue: {
                structure: ['ملاحظة', 'تساؤل', 'استكشاف', 'استنتاج'],
                example_flow: [
                    'انظروا! هناك شيء غريب هناك',
                    'ما هذا يا ترى؟',
                    'دعونا نقترب ونرى',
                    'آه! إنه قط صغير!'
                ],
                voice_directions: [
                    'بصوت متحمس ومتفاجئ',
                    'بنبرة فضولية',
                    'بحذر وترقب',
                    'بفرح وإعجاب'
                ]
            },

            problem_solving_dialogue: {
                structure: ['تحديد المشكلة', 'عصف ذهني', 'اختيار الحل', 'تنفيذ'],
                example_flow: [
                    'يبدو أن القط خائف وجائع',
                    'ماذا يمكننا أن نفعل لمساعدته؟',
                    'لدي فكرة! لنعطيه بعض الطعام',
                    'ممتاز! هيا نجرب ذلك'
                ],
                voice_directions: [
                    'بصوت قلق ومتعاطف',
                    'بنبرة تفكير',
                    'بحماس وثقة',
                    'بتشجيع وتفاؤل'
                ]
            },

            emotional_dialogue: {
                structure: ['التعبير عن المشاعر', 'المشاركة', 'التعاطف', 'الدعم'],
                example_flow: [
                    'أشعر بالحزن لهذا القط الصغير',
                    'أنا أيضاً أشعر بنفس الشعور',
                    'المسكين، يبدو وحيداً',
                    'لا تقلق، سنعتني به معاً'
                ],
                voice_directions: [
                    'بصوت حزين ومتأثر',
                    'بتفهم ومشاركة',
                    'بحنان وعطف',
                    'بتصميم وحب'
                ]
            },

            celebration_dialogue: {
                structure: ['الفرح', 'المشاركة', 'الشكر', 'التطلع للمستقبل'],
                example_flow: [
                    'يييييي! نجحنا في مساعدته!',
                    'أنا سعيد جداً!',
                    'شكراً لكم جميعاً',
                    'سنكون أصدقاء رائعين!'
                ],
                voice_directions: [
                    'بصوت مبتهج ومتحمس',
                    'بفرح وسرور',
                    'بامتنان وتقدير',
                    'بتفاؤل وحب'
                ]
            }
        };
    }

    // ===== الحالات العاطفية =====
    
    getEmotionalStates() {
        return {
            excitement: {
                verbal_expressions: ['واااو!', 'يا الله!', 'مذهل!', 'رائع!'],
                body_language: ['يقفز', 'يصفق', 'يركض', 'يرقص'],
                voice_changes: ['صوت عالي', 'سرعة في الكلام', 'نبرة مرحة'],
                dialogue_patterns: [
                    'لا أصدق ما أراه!',
                    'هذا أفضل يوم في حياتي!',
                    'أريد أن أخبر الجميع!'
                ]
            },

            curiosity: {
                verbal_expressions: ['ماذا؟', 'كيف؟', 'لماذا؟', 'أين؟'],
                body_language: ['يميل للأمام', 'يحدق', 'يلمس بحذر'],
                voice_changes: ['نبرة استفهام', 'توقفات تفكير', 'همهمات'],
                dialogue_patterns: [
                    'أتساءل ما هذا...',
                    'لم أر شيئاً مثل هذا من قبل',
                    'كيف يعمل هذا يا ترى؟'
                ]
            },

            empathy: {
                verbal_expressions: ['المسكين', 'أشعر بالحزن له', 'يؤلمني أن أراه هكذا'],
                body_language: ['يمد يده بلطف', 'يحتضن', 'يربت بحنان'],
                voice_changes: ['صوت ناعم', 'نبرة حنونة', 'بطء في الكلام'],
                dialogue_patterns: [
                    'لا بد أنه يشعر بالخوف',
                    'أريد أن أساعده',
                    'سأعتني به كما لو كان أخي'
                ]
            },

            determination: {
                verbal_expressions: ['سأفعل ذلك!', 'لن أستسلم!', 'معاً سننجح!'],
                body_language: ['يقف بثبات', 'يضع يديه على خصره', 'ينظر بتصميم'],
                voice_changes: ['صوت قوي', 'نبرة واثقة', 'وضوح في النطق'],
                dialogue_patterns: [
                    'لن أتركه وحيداً',
                    'سأجد طريقة لمساعدته',
                    'الأصدقاء لا يتخلون عن بعضهم'
                ]
            }
        };
    }

    // ===== أنماط المحادثة =====
    
    getConversationPatterns() {
        return {
            sibling_conversation: {
                dynamics: 'تفاعل طبيعي بين الأشقاء',
                characteristics: [
                    'مقاطعة أحياناً',
                    'تنافس ودود',
                    'مشاركة الأفكار',
                    'دعم متبادل'
                ],
                example_exchanges: [
                    {
                        child1: 'أنا رأيته أولاً!',
                        child2: 'لكنني أنا من سمع صوته!',
                        resolution: 'دعونا نساعده معاً!'
                    }
                ]
            },

            parent_child_conversation: {
                dynamics: 'حوار تعليمي وحنون',
                characteristics: [
                    'الوالد يوجه بحكمة',
                    'الطفل يسأل ويتعلم',
                    'تبادل المشاعر',
                    'تعزيز القيم'
                ],
                example_exchanges: [
                    {
                        child: 'بابا، هل يمكنني الاحتفاظ به؟',
                        parent: 'هذا قرار كبير يا حبيبي، ما رأيك في المسؤوليات؟',
                        child: 'سأطعمه وأعتني به كل يوم!'
                    }
                ]
            },

            group_conversation: {
                dynamics: 'حوار جماعي متوازن',
                characteristics: [
                    'كل شخص له دور',
                    'تبادل الأفكار',
                    'اتخاذ قرارات جماعية',
                    'دعم الفريق'
                ],
                example_pattern: [
                    'طرح الموضوع',
                    'مشاركة الآراء',
                    'مناقشة الخيارات',
                    'الوصول لاتفاق'
                ]
            }
        };
    }

    // ===== التعبيرات الثقافية =====
    
    getCulturalExpressions() {
        return {
            traditional_openings: [
                'بسم الله نبدأ',
                'الحمد لله',
                'ما شاء الله',
                'بارك الله فيك'
            ],

            family_expressions: [
                'يا حبيب قلبي',
                'يا عزيز عليّ',
                'يا نور عيني',
                'يا غالي'
            ],

            encouragement_phrases: [
                'أحسنت!',
                'ممتاز!',
                'بارك الله فيك!',
                'فخور بك!'
            ],

            comfort_expressions: [
                'لا تخف',
                'كله خير',
                'ربنا معانا',
                'سيكون كل شيء بخير'
            ],

            celebration_phrases: [
                'الله أكبر!',
                'الحمد لله!',
                'يا فرحتي!',
                'نجحنا بحمد الله!'
            ]
        };
    }

    // ===== اللغة المناسبة للأعمار =====
    
    getAgeAppropriateLanguage() {
        return {
            young_children: { // 3-5 سنوات
                vocabulary: {
                    simple_words: ['كبير', 'صغير', 'جميل', 'لطيف'],
                    action_words: ['يجري', 'يلعب', 'يأكل', 'ينام'],
                    emotion_words: ['سعيد', 'حزين', 'خائف', 'فرحان']
                },
                sentence_structure: {
                    length: 'قصيرة (3-5 كلمات)',
                    complexity: 'بسيطة',
                    examples: ['القط صغير', 'أنا أحبه', 'هيا نلعب']
                },
                dialogue_style: {
                    repetition: 'مفيدة للتعلم',
                    rhythm: 'إيقاع واضح',
                    sounds: 'أصوات مقلدة كثيرة'
                }
            },

            middle_children: { // 6-8 سنوات
                vocabulary: {
                    expanded_words: ['مسؤولية', 'صداقة', 'مساعدة', 'اهتمام'],
                    descriptive_words: ['شجاع', 'حنون', 'ذكي', 'مفيد'],
                    concept_words: ['يفهم', 'يقرر', 'يخطط', 'يحل']
                },
                sentence_structure: {
                    length: 'متوسطة (5-8 كلمات)',
                    complexity: 'متوسطة مع ربط',
                    examples: ['لأن القط خائف، سنساعده', 'عندما نعتني به، سيصبح سعيداً']
                },
                dialogue_style: {
                    reasoning: 'تفسير الأسباب',
                    consequences: 'ربط الأفعال بالنتائج',
                    emotions: 'تعبير أعمق عن المشاعر'
                }
            },

            older_children: { // 9-12 سنة
                vocabulary: {
                    complex_words: ['التزام', 'تضحية', 'مبادئ', 'قرارات'],
                    abstract_concepts: ['عدالة', 'حكمة', 'شجاعة', 'تسامح'],
                    mature_expressions: ['أدرك أن', 'أعتقد أن', 'من المهم أن']
                },
                sentence_structure: {
                    length: 'طويلة (8-12 كلمة)',
                    complexity: 'معقدة مع جمل فرعية',
                    examples: ['أدرك أن الاعتناء بالحيوان مسؤولية كبيرة تتطلب التزاماً يومياً']
                },
                dialogue_style: {
                    analysis: 'تحليل المواقف',
                    moral_reasoning: 'تفكير أخلاقي',
                    future_planning: 'التفكير في المستقبل'
                }
            }
        };
    }

    // ===== دوال توليد الحوارات =====
    
    generateCharacterDialogue(character, situation, emotion, age_group) {
        const personality = this.characterPersonalities[character.type];
        const emotional_state = this.emotionalStates[emotion];
        const age_language = this.ageAppropriateLanguage[age_group];
        
        return this.constructDialogue(personality, emotional_state, age_language, situation);
    }

    constructDialogue(personality, emotion, language, situation) {
        // اختيار التعبير المناسب
        const expression = this.selectExpression(personality, emotion);
        
        // تطبيق القواعد اللغوية للعمر
        const age_appropriate = this.adaptToAge(expression, language);
        
        // إضافة التوجيهات الصوتية
        const voice_direction = this.addVoiceDirection(emotion, personality);
        
        return {
            text: age_appropriate,
            voice_direction: voice_direction,
            body_language: emotion.body_language[0],
            emotional_tone: emotion.voice_changes.join(', ')
        };
    }

    selectExpression(personality, emotion) {
        // دمج تعبيرات الشخصية مع الحالة العاطفية
        const personality_phrases = personality.typical_phrases;
        const emotion_expressions = emotion.dialogue_patterns;
        
        // اختيار عشوائي مناسب
        const combined = [...personality_phrases, ...emotion_expressions];
        return combined[Math.floor(Math.random() * combined.length)];
    }

    adaptToAge(expression, language) {
        // تبسيط أو تعقيد التعبير حسب العمر
        if (language.sentence_structure.complexity === 'بسيطة') {
            return this.simplifyExpression(expression);
        } else if (language.sentence_structure.complexity === 'معقدة مع جمل فرعية') {
            return this.complexifyExpression(expression);
        }
        return expression;
    }

    simplifyExpression(expression) {
        // تبسيط للأطفال الصغار
        return expression.split(' ').slice(0, 5).join(' ');
    }

    complexifyExpression(expression) {
        // إضافة تعقيد للأطفال الأكبر
        const connectors = ['لأن', 'عندما', 'إذا', 'بينما'];
        const connector = connectors[Math.floor(Math.random() * connectors.length)];
        return `${expression}، ${connector}...`;
    }

    addVoiceDirection(emotion, personality) {
        return `${emotion.voice_changes.join(', ')} مع ${personality.voice_characteristics.tone}`;
    }

    // ===== توليد محادثة كاملة =====
    
    generateFullConversation(characters, situation, age_group) {
        const conversation = [];
        const dialogue_style = this.dialogueStyles[situation.type];
        
        dialogue_style.structure.forEach((step, index) => {
            characters.forEach(character => {
                const emotion = this.determineEmotion(step, character);
                const dialogue = this.generateCharacterDialogue(
                    character, 
                    situation, 
                    emotion, 
                    age_group
                );
                
                conversation.push({
                    character: character.name,
                    step: step,
                    dialogue: dialogue,
                    sequence: index
                });
            });
        });
        
        return this.organizeConversationFlow(conversation);
    }

    determineEmotion(step, character) {
        const emotion_map = {
            'ملاحظة': 'curiosity',
            'تساؤل': 'curiosity',
            'استكشاف': 'excitement',
            'استنتاج': 'excitement',
            'تحديد المشكلة': 'empathy',
            'عصف ذهني': 'curiosity',
            'اختيار الحل': 'determination',
            'تنفيذ': 'determination'
        };
        
        return emotion_map[step] || 'excitement';
    }

    organizeConversationFlow(conversation) {
        // ترتيب المحادثة بشكل طبيعي
        return conversation.sort((a, b) => {
            if (a.sequence !== b.sequence) {
                return a.sequence - b.sequence;
            }
            // ترتيب الشخصيات داخل كل خطوة
            return a.character.localeCompare(b.character);
        });
    }
}

// تصدير الفئة للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedDialogueSystem;
}
