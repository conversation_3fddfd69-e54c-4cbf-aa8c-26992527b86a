// ملف إصلاح الأخطاء الشامل
// Dr.<PERSON>, SUST -BME, @ 2025

console.log('🔧 بدء إصلاح الأخطاء الشامل...');

// إصلاح مشاكل التخزين المحلي
function fixLocalStorageIssues() {
    console.log('📦 فحص التخزين المحلي...');
    
    try {
        // اختبار التخزين المحلي
        if (typeof Storage === "undefined") {
            console.error('❌ التخزين المحلي غير مدعوم');
            return false;
        }
        
        // تنظيف البيانات التالفة
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.includes('story') || key.includes('collaborative')) {
                try {
                    const data = localStorage.getItem(key);
                    JSON.parse(data); // اختبار صحة JSON
                    console.log(`✅ ${key} صحيح`);
                } catch (e) {
                    console.warn(`⚠️ ${key} تالف، سيتم مسحه`);
                    localStorage.removeItem(key);
                }
            }
        });
        
        return true;
    } catch (error) {
        console.error('❌ خطأ في التخزين المحلي:', error);
        return false;
    }
}

// إصلاح مشاكل DOM
function fixDOMIssues() {
    console.log('🌐 فحص عناصر DOM...');
    
    // التأكد من وجود العناصر المطلوبة
    const requiredElements = [
        'start-collaboration',
        'back-to-home',
        'add-to-story',
        'user-input',
        'story-output'
    ];
    
    let missingElements = [];
    
    requiredElements.forEach(id => {
        const element = document.getElementById(id);
        if (!element) {
            missingElements.push(id);
        } else {
            console.log(`✅ العنصر ${id} موجود`);
        }
    });
    
    if (missingElements.length > 0) {
        console.warn('⚠️ عناصر مفقودة:', missingElements);
        return false;
    }
    
    return true;
}

// إصلاح مشاكل الأحداث
function fixEventListeners() {
    console.log('🎯 فحص معالجات الأحداث...');
    
    try {
        // إضافة معالجات الأحداث المفقودة
        const startCollabBtn = document.getElementById('start-collaboration');
        if (startCollabBtn && !startCollabBtn.onclick) {
            startCollabBtn.addEventListener('click', function() {
                console.log('🚀 تم النقر على زر التعاون');
                startCollaborativeStoryFixed();
            });
            console.log('✅ تم إضافة معالج زر التعاون');
        }
        
        const backHomeBtn = document.getElementById('back-to-home');
        if (backHomeBtn && !backHomeBtn.onclick) {
            backHomeBtn.addEventListener('click', function() {
                console.log('🏠 تم النقر على زر العودة للصفحة الرئيسية');
                window.location.href = 'index.html';
            });
            console.log('✅ تم إضافة معالج زر العودة للصفحة الرئيسية');
        }
        
        const addStoryBtn = document.getElementById('add-to-story');
        if (addStoryBtn && !addStoryBtn.onclick) {
            addStoryBtn.addEventListener('click', function() {
                console.log('📖 تم النقر على زر إضافة القصة');
                addToStoryFixed();
            });
            console.log('✅ تم إضافة معالج زر إضافة القصة');
        }
        
        return true;
    } catch (error) {
        console.error('❌ خطأ في معالجات الأحداث:', error);
        return false;
    }
}

// دالة بديلة لبدء التعاون
function startCollaborativeStoryFixed() {
    console.log('🚀 بدء التعاون المصحح...');
    
    try {
        // جمع البيانات
        const userData = collectUserInputsFixed();
        
        // إنشاء بيانات التعاون
        const collaborativeData = {
            mode: 'collaborative',
            timestamp: new Date().toISOString(),
            userInputs: userData,
            settings: {
                language: 'arabic',
                format: 'interactive',
                includeVoiceDirections: true,
                culturalElements: true
            }
        };
        
        // حفظ البيانات
        localStorage.setItem('collaborativeStoryData', JSON.stringify(collaborativeData));
        console.log('💾 تم حفظ بيانات التعاون');
        
        // إظهار تأثير الانتقال
        showTransitionEffectFixed();
        
        // الانتقال للصفحة
        setTimeout(() => {
            window.location.href = 'collaborative-writing.html';
        }, 1500);
        
    } catch (error) {
        console.error('❌ خطأ في بدء التعاون:', error);
        alert('حدث خطأ في بدء التعاون. يرجى المحاولة مرة أخرى.');
    }
}

// دالة بديلة لجمع البيانات
function collectUserInputsFixed() {
    console.log('📊 جمع البيانات المصحح...');
    
    const userInputs = {};
    
    try {
        // جمع المدخلات النصية
        const textInputs = document.querySelectorAll('input[type="text"], input[type="number"], textarea, select');
        textInputs.forEach(input => {
            if (input.id && input.value) {
                userInputs[input.id] = input.value;
                console.log(`📝 تم جمع ${input.id}: ${input.value}`);
            }
        });
        
        // جمع العناصر المحددة
        const selectedElements = [];
        document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
            selectedElements.push({
                type: checkbox.name || 'element',
                value: checkbox.value,
                label: checkbox.value
            });
            console.log(`☑️ تم جمع عنصر: ${checkbox.value}`);
        });
        
        // جمع الخيارات المحددة
        document.querySelectorAll('input[type="radio"]:checked').forEach(radio => {
            userInputs[radio.name] = radio.value;
            console.log(`🔘 تم جمع خيار: ${radio.name} = ${radio.value}`);
        });
        
        userInputs.storyElements = selectedElements;
        userInputs.preferences = {
            storyTone: userInputs['story-tone'] || 'gentle',
            storyLength: userInputs['story-length'] || 'medium',
            language: 'arabic',
            culturalElements: true,
            voiceDirections: true
        };
        
        userInputs.collectionTime = new Date().toISOString();
        
        console.log('✅ تم جمع البيانات بنجاح:', userInputs);
        return userInputs;
        
    } catch (error) {
        console.error('❌ خطأ في جمع البيانات:', error);
        return {
            preferences: {
                storyTone: 'gentle',
                storyLength: 'medium',
                language: 'arabic'
            },
            collectionTime: new Date().toISOString()
        };
    }
}

// دالة بديلة لإضافة القصة
function addToStoryFixed() {
    console.log('📖 إضافة القصة المصححة...');
    
    try {
        const userInput = document.getElementById('user-input');
        if (!userInput || !userInput.value.trim()) {
            alert('يرجى كتابة شيء أولاً!');
            return;
        }
        
        const inputText = userInput.value.trim();
        console.log('📝 النص المدخل:', inputText);
        
        // تحسين النص
        const enhancedText = enhanceUserInputFixed(inputText);
        console.log('✨ النص المحسن:', enhancedText);
        
        // إضافة النص للقصة
        appendToStoryFixed(enhancedText);
        
        // مسح المدخل
        userInput.value = '';
        
        console.log('✅ تم إضافة النص للقصة بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إضافة القصة:', error);
        alert('حدث خطأ في إضافة النص. يرجى المحاولة مرة أخرى.');
    }
}

// دالة بديلة لتحسين النص
function enhanceUserInputFixed(input) {
    console.log('✨ تحسين النص...');
    
    let enhanced = input;
    
    try {
        // تحميل البيانات المحفوظة
        const savedData = localStorage.getItem('collaborativeStoryData');
        let userData = null;
        
        if (savedData) {
            userData = JSON.parse(savedData).userInputs;
        }
        
        // إضافة افتتاحية عربية
        if (!enhanced.startsWith('كان يا ما كان')) {
            enhanced = `كان يا ما كان، ${enhanced}`;
        }
        
        // تطبيق النبرة
        if (userData && userData.preferences) {
            const tone = userData.preferences.storyTone;
            
            if (tone === 'exciting') {
                enhanced = enhanced.replace(/قال/g, 'صرخ بحماس');
                enhanced = enhanced.replace(/مشى/g, 'جرى بسرعة');
                enhanced += '\n\nوفجأة، بدأت مغامرة مثيرة ومشوقة!';
            } else if (tone === 'gentle') {
                enhanced = enhanced.replace(/قال/g, 'همس بلطف');
                enhanced += '\n\nوفي جو من السكينة والهدوء...';
            } else if (tone === 'funny') {
                enhanced += '\n\nوضحك الجميع بسعادة! هههههه!';
            } else if (tone === 'educational') {
                enhanced += '\n\nوتعلم شيئاً جديداً ومفيداً من هذه التجربة.';
            }
        }
        
        // استبدال الأسماء العامة
        if (userData && userData['child-name']) {
            enhanced = enhanced.replace(/البطل|الطفل|الشخصية الرئيسية/g, userData['child-name']);
        }
        
        // إضافة توجيهات صوتية
        enhanced += '\n\n*(بصوت مرح ومتحمس)*';
        
        // إضافة عناصر تفاعلية
        if (userData && userData.storyElements) {
            userData.storyElements.forEach(element => {
                if (element.value === 'animals') {
                    enhanced += '\n\n🐱 "مياو مياو!" قالت القطة الصغيرة.';
                } else if (element.value === 'adventure') {
                    enhanced += '\n\n🗺️ وهكذا بدأت مغامرة لا تُنسى!';
                } else if (element.value === 'magic') {
                    enhanced += '\n\n✨ وحدث شيء سحري رائع!';
                } else if (element.value === 'friendship') {
                    enhanced += '\n\n👫 وكونوا صداقة جميلة تدوم للأبد.';
                }
            });
        }
        
        console.log('✅ تم تحسين النص بنجاح');
        return enhanced;
        
    } catch (error) {
        console.error('❌ خطأ في تحسين النص:', error);
        return enhanced; // إرجاع النص الأصلي في حالة الخطأ
    }
}

// دالة بديلة لإضافة النص للقصة
function appendToStoryFixed(content) {
    console.log('📚 إضافة النص للقصة...');
    
    try {
        const storyOutput = document.getElementById('story-output');
        if (!storyOutput) {
            console.error('❌ عنصر عرض القصة غير موجود');
            return;
        }
        
        // إنشاء عنصر جديد للمحتوى
        const storyPart = document.createElement('div');
        storyPart.className = 'story-part mb-4 p-4 bg-white rounded-lg border-r-4 border-blue-500 shadow-sm';
        storyPart.innerHTML = `
            <div class="text-sm text-gray-500 mb-2">
                <i class="fas fa-feather-alt ml-1"></i>
                ${new Date().toLocaleTimeString('ar-SA')}
            </div>
            <div class="story-text text-lg leading-relaxed">${content.replace(/\n/g, '<br>')}</div>
        `;
        
        // مسح الرسالة الافتراضية إذا كانت موجودة
        if (storyOutput.innerHTML.includes('ابدأ بكتابة أفكارك')) {
            storyOutput.innerHTML = '';
        }
        
        // إضافة المحتوى الجديد
        storyOutput.appendChild(storyPart);
        
        // التمرير للأسفل
        storyOutput.scrollTop = storyOutput.scrollHeight;
        
        console.log('✅ تم إضافة النص للقصة بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إضافة النص للقصة:', error);
    }
}

// دالة بديلة لتأثير الانتقال
function showTransitionEffectFixed() {
    console.log('✨ عرض تأثير الانتقال...');
    
    try {
        // إنشاء تأثير الانتقال
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.5s ease;
        `;
        
        overlay.innerHTML = `
            <div style="text-align: center; color: white;">
                <div style="width: 60px; height: 60px; border: 4px solid rgba(255,255,255,0.3); border-top: 4px solid white; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
                <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 20px;">جاري التحضير للكتابة التعاونية...</h3>
                <p style="opacity: 0.9;">سيتم نقلك خلال لحظات</p>
            </div>
        `;
        
        // إضافة CSS للدوران
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(overlay);
        
        // إظهار التأثير
        setTimeout(() => {
            overlay.style.opacity = '1';
        }, 100);
        
        console.log('✅ تم عرض تأثير الانتقال');
        
    } catch (error) {
        console.error('❌ خطأ في تأثير الانتقال:', error);
    }
}

// تشغيل الإصلاحات عند تحميل الصفحة
function runAllFixes() {
    console.log('🔧 تشغيل جميع الإصلاحات...');
    
    const results = {
        localStorage: fixLocalStorageIssues(),
        dom: fixDOMIssues(),
        events: fixEventListeners()
    };
    
    const successCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`📊 نتائج الإصلاح: ${successCount}/${totalCount} نجح`);
    
    if (successCount === totalCount) {
        console.log('✅ تم إصلاح جميع المشاكل بنجاح!');
    } else {
        console.warn('⚠️ بعض المشاكل لم يتم إصلاحها');
    }
    
    return results;
}

// تصدير الدوال للاستخدام العام
if (typeof window !== 'undefined') {
    window.fixErrors = {
        runAllFixes,
        startCollaborativeStoryFixed,
        addToStoryFixed,
        enhanceUserInputFixed,
        collectUserInputsFixed
    };
}

// تشغيل الإصلاحات تلقائياً
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllFixes);
} else {
    runAllFixes();
}

console.log('🎉 تم تحميل ملف الإصلاحات بنجاح!');
