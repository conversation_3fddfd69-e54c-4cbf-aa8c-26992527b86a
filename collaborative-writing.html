<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الكتابة التعاونية للقصص - ركن الأطفال</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .arabic-text {
            font-family: '<PERSON>i', serif;
            line-height: 1.8;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .writing-workspace {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            min-height: 80vh;
        }

        @media (max-width: 768px) {
            .writing-workspace {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        .input-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .output-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .story-output {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            min-height: 400px;
            border-right: 4px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            white-space: pre-wrap;
        }

        .input-field {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            resize: vertical;
        }

        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .action-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .action-button:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .secondary-button {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .secondary-button:hover {
            background: linear-gradient(135deg, #5a6268, #343a40);
            transform: translateY(-1px);
        }

        .progress-indicator {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.5s ease;
            width: 0%;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 10px;
            color: #667eea;
            font-style: italic;
            margin: 1rem 0;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .suggestion-card {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 1rem;
            margin: 0.5rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .suggestion-card:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateX(5px);
        }

        .floating-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            font-size: 1.5rem;
            opacity: 0.6;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .collaboration-header {
            text-align: center;
            margin-bottom: 2rem;
            color: white;
        }

        .collaboration-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .collaboration-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .story-section {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-right: 3px solid #667eea;
        }

        .section-title {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .word-count {
            font-size: 0.9rem;
            color: #6c757d;
            text-align: left;
            margin-top: 0.5rem;
        }

        .collaboration-tips {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .tip-icon {
            color: #ffc107;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Floating Particles -->
    <div class="floating-particles" id="particles"></div>

    <!-- Header -->
    <header class="collaboration-header py-6">
        <h1 class="collaboration-title arabic-text">🎭 ورشة الكتابة التعاونية 📚</h1>
        <p class="collaboration-subtitle">نكتب معاً قصة رائعة خطوة بخطوة</p>
    </header>

    <!-- Main Content -->
    <div class="container mx-auto px-4">
        <!-- Progress Indicator -->
        <div class="progress-indicator">
            <div class="flex justify-between items-center mb-2">
                <span class="font-semibold text-gray-700">تقدم الكتابة</span>
                <span id="progress-percentage" class="text-blue-600 font-bold">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="flex justify-between text-sm text-gray-600 mt-2">
                <span>البداية</span>
                <span>التطوير</span>
                <span>الذروة</span>
                <span>النهاية</span>
            </div>
        </div>

        <!-- Writing Workspace -->
        <div class="writing-workspace">
            <!-- Input Panel -->
            <div class="input-panel">
                <h2 class="text-2xl font-bold text-gray-800 mb-4 arabic-text">
                    <i class="fas fa-edit text-blue-600 ml-2"></i>
                    مساحة الإبداع
                </h2>

                <!-- Collaboration Tips -->
                <div class="collaboration-tips">
                    <h3 class="font-semibold text-gray-700 mb-2">
                        <i class="fas fa-lightbulb tip-icon"></i>
                        نصائح للكتابة التعاونية
                    </h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• شاركنا أفكارك وسنحولها إلى قصة جميلة</li>
                        <li>• اكتب ما تريد أن يحدث في القصة</li>
                        <li>• لا تقلق من الأخطاء، سنصححها معاً</li>
                        <li>• كن مبدعاً واتركنا نضيف السحر!</li>
                    </ul>
                </div>

                <!-- Current Section -->
                <div class="story-section">
                    <div class="section-title" id="current-section">البداية - تقديم الشخصيات</div>
                    <p class="text-sm text-gray-600" id="section-description">
                        أخبرنا عن الشخصية الرئيسية وأين تحدث القصة
                    </p>
                </div>

                <!-- User Input -->
                <div class="mb-4">
                    <label for="user-input" class="block text-gray-700 font-semibold mb-2">
                        أفكارك ومساهمتك:
                    </label>
                    <textarea 
                        id="user-input" 
                        class="input-field arabic-text" 
                        rows="6" 
                        placeholder="اكتب أفكارك هنا... مثلاً: أريد قصة عن طفل يحب الحيوانات ويجد قطة صغيرة في الحديقة..."
                    ></textarea>
                    <div class="word-count" id="word-count">0 كلمة</div>
                </div>

                <!-- Suggestions -->
                <div id="suggestions-container" class="mb-4 hidden">
                    <h4 class="font-semibold text-gray-700 mb-2">
                        <i class="fas fa-magic text-purple-500 ml-1"></i>
                        اقتراحات لمساعدتك:
                    </h4>
                    <div id="suggestions-list"></div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-3">
                    <button id="add-to-story" class="action-button flex-1">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة للقصة
                    </button>
                    <button id="get-suggestions" class="secondary-button">
                        <i class="fas fa-lightbulb ml-1"></i>
                        اقتراحات
                    </button>
                </div>

                <!-- Navigation -->
                <div class="flex justify-between mt-6">
                    <button id="back-to-maker" class="secondary-button">
                        <i class="fas fa-arrow-right ml-1"></i>
                        العودة للصانع
                    </button>
                    <button id="next-section" class="action-button" disabled>
                        <i class="fas fa-arrow-left ml-1"></i>
                        القسم التالي
                    </button>
                </div>
            </div>

            <!-- Output Panel -->
            <div class="output-panel">
                <h2 class="text-2xl font-bold text-gray-800 mb-4 arabic-text">
                    <i class="fas fa-book-open text-green-600 ml-2"></i>
                    القصة المتطورة
                </h2>

                <!-- Typing Indicator -->
                <div class="typing-indicator" id="typing-indicator">
                    <span>الذكاء الاصطناعي يكتب...</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>

                <!-- Story Output -->
                <div class="story-output arabic-text" id="story-output">
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-feather-alt text-4xl mb-4 text-gray-400"></i>
                        <p>ابدأ بكتابة أفكارك وسنبني القصة معاً...</p>
                        <p class="text-sm mt-2">كل إضافة ستظهر هنا بشكل جميل ومنسق</p>
                    </div>
                </div>

                <!-- Story Actions -->
                <div class="flex justify-between mt-4">
                    <button id="save-story" class="secondary-button" disabled>
                        <i class="fas fa-save ml-1"></i>
                        حفظ القصة
                    </button>
                    <button id="export-story" class="secondary-button" disabled>
                        <i class="fas fa-download ml-1"></i>
                        تصدير
                    </button>
                    <button id="share-story" class="secondary-button" disabled>
                        <i class="fas fa-share ml-1"></i>
                        مشاركة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="JS/collaborative-writing.js"></script>
</body>
</html>
