<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قصتك الجديدة - مولد القصص العربية للأطفال</title>
    <meta name="description" content="قصة عربية مخصصة لطفلك مع دروس أخلاقية قيمة">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .arabic-text {
            font-family: 'Tajawal', sans-serif;
        }
        
        .story-text {
            font-family: 'Amiri', serif;
            line-height: 2.2;
            font-size: 1.1rem;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .fade-in {
            animation: fadeIn 1s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .story-container {
            max-height: 70vh;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }
        
        .story-container::-webkit-scrollbar {
            width: 8px;
        }
        
        .story-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        .story-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
        
        .story-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .story-metadata {
            background: rgba(255, 255, 255, 0.05);
            border-left: 4px solid #fbbf24;
        }
        
        .action-button {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.2);
        }
        
        .story-title {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .moral-lesson {
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .character-info {
            display: inline-flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            margin: 5px;
        }
        
        .story-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .print-hidden {
            display: none;
        }

        /* Reading Guide Styles */
        .reading-guide {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .voice-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 10px;
        }

        .character-voice-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 12px;
            border-radius: 8px;
            margin: 5px 0;
            font-size: 0.9rem;
        }

        .sound-effect-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 12px;
            border-radius: 8px;
            margin: 5px 0;
            font-size: 0.9rem;
        }

        .reading-tip-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 15px;
            border-radius: 8px;
            margin: 5px 0;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .reading-tip-item i {
            margin-left: 8px;
            color: #fbbf24;
        }
        
        @media print {
            body {
                background: white !important;
                color: black !important;
            }
            
            .glass-effect {
                background: white !important;
                backdrop-filter: none !important;
                border: 1px solid #ccc !important;
            }
            
            .print-hidden {
                display: none !important;
            }
            
            .story-text {
                color: black !important;
            }
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8">
        <!-- Loading State -->
        <div id="loading-state" class="text-center">
            <div class="floating-animation">
                <i class="fas fa-magic text-6xl text-yellow-300 mb-6 loading-spinner"></i>
                <h2 class="text-3xl font-bold text-white mb-4 arabic-text">جاري إنشاء قصتك السحرية...</h2>
                <p class="text-purple-200 arabic-text">يرجى الانتظار بينما نحضر لك قصة رائعة</p>
            </div>
        </div>

        <!-- Story Content -->
        <div id="story-content" class="hidden fade-in">
            <!-- Header -->
            <header class="text-center mb-8">
                <h1 id="story-title" class="text-4xl font-bold story-title mb-4 arabic-text"></h1>
                <div class="story-metadata p-4 rounded-lg mb-6">
                    <div id="story-info" class="flex flex-wrap justify-center items-center gap-2"></div>
                </div>
            </header>

            <!-- Story Container -->
            <div class="max-w-4xl mx-auto">
                <div class="glass-effect rounded-3xl p-8 shadow-2xl">
                    <!-- Story Text -->
                    <div id="story-text" class="story-container story-text text-white leading-relaxed mb-6"></div>
                    
                    <!-- Moral Lesson -->
                    <div id="moral-lesson" class="moral-lesson text-white text-center">
                        <h3 class="text-xl font-bold mb-3 arabic-text">
                            <i class="fas fa-lightbulb ml-2"></i>
                            الدرس المستفاد
                        </h3>
                        <p id="lesson-text" class="text-lg arabic-text"></p>
                    </div>

                    <!-- Interactive Reading Guide -->
                    <div id="reading-guide" class="reading-guide text-white mt-6 hidden">
                        <h3 class="text-xl font-bold mb-4 arabic-text text-center">
                            <i class="fas fa-microphone ml-2"></i>
                            دليل القراءة التفاعلية 🎭
                        </h3>

                        <div class="grid md:grid-cols-2 gap-6">
                            <!-- Character Voices -->
                            <div class="voice-section">
                                <h4 class="text-lg font-semibold mb-3 arabic-text">
                                    <i class="fas fa-users ml-2"></i>
                                    أصوات الشخصيات
                                </h4>
                                <div id="character-voices" class="space-y-2"></div>
                            </div>

                            <!-- Sound Effects -->
                            <div class="voice-section">
                                <h4 class="text-lg font-semibold mb-3 arabic-text">
                                    <i class="fas fa-volume-up ml-2"></i>
                                    التأثيرات الصوتية
                                </h4>
                                <div id="sound-effects" class="space-y-2"></div>
                            </div>
                        </div>

                        <!-- Reading Tips -->
                        <div class="reading-tips mt-6">
                            <h4 class="text-lg font-semibold mb-3 arabic-text text-center">
                                <i class="fas fa-star ml-2"></i>
                                نصائح للقراءة الممتعة
                            </h4>
                            <div id="reading-tips" class="grid md:grid-cols-2 gap-3"></div>
                        </div>

                        <!-- Toggle Button -->
                        <div class="text-center mt-4">
                            <button id="toggle-guide" class="text-sm text-purple-200 hover:text-white transition-colors">
                                <i class="fas fa-eye-slash ml-1"></i>
                                إخفاء الدليل
                            </button>
                        </div>
                    </div>

                    <!-- Show Reading Guide Button -->
                    <div id="show-guide-btn" class="text-center mt-4">
                        <button class="text-purple-200 hover:text-white transition-colors arabic-text" onclick="toggleReadingGuide()">
                            <i class="fas fa-microphone ml-2"></i>
                            عرض دليل القراءة التفاعلية 🎭
                        </button>
                    </div>

                    <!-- Story Statistics -->
                    <div id="story-stats" class="story-stats print-hidden">
                        <div class="stat-item">
                            <i class="fas fa-clock text-yellow-300 text-xl mb-2"></i>
                            <div class="text-sm text-purple-200 arabic-text">وقت القراءة</div>
                            <div id="reading-time" class="text-white font-bold"></div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-align-left text-yellow-300 text-xl mb-2"></i>
                            <div class="text-sm text-purple-200 arabic-text">عدد الكلمات</div>
                            <div id="word-count" class="text-white font-bold"></div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-user text-yellow-300 text-xl mb-2"></i>
                            <div class="text-sm text-purple-200 arabic-text">العمر المناسب</div>
                            <div id="target-age" class="text-white font-bold"></div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-star text-yellow-300 text-xl mb-2"></i>
                            <div class="text-sm text-purple-200 arabic-text">نوع القصة</div>
                            <div id="story-style" class="text-white font-bold"></div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap justify-center gap-4 mt-8 print-hidden">
                    <button id="read-again-btn" class="action-button bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-full arabic-text">
                        <i class="fas fa-redo ml-2"></i>
                        اقرأ مرة أخرى
                    </button>
                    
                    <button id="new-story-btn" class="action-button bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-full arabic-text">
                        <i class="fas fa-plus ml-2"></i>
                        قصة جديدة
                    </button>
                    
                    <button id="share-btn" class="action-button bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-bold py-3 px-6 rounded-full arabic-text">
                        <i class="fas fa-share ml-2"></i>
                        مشاركة
                    </button>
                    
                    <button id="print-btn" class="action-button bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-bold py-3 px-6 rounded-full arabic-text">
                        <i class="fas fa-print ml-2"></i>
                        طباعة
                    </button>
                    
                    <button id="save-btn" class="action-button bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-full arabic-text">
                        <i class="fas fa-save ml-2"></i>
                        حفظ القصة
                    </button>
                </div>

                <!-- Author Information -->
                <div class="text-center mt-12 print-hidden">
                    <div class="glass-effect rounded-2xl p-6">
                        <h3 class="text-xl font-bold text-white mb-3 arabic-text">
                            <i class="fas fa-user-graduate ml-2 text-yellow-300"></i>
                            معلومات المؤلف
                        </h3>
                        <div class="text-purple-200 arabic-text">
                            <p class="mb-2"><strong>د. محمد يعقوب إسماعيل</strong></p>
                            <p class="mb-2">جامعة السودان للعلوم والتكنولوجيا - الهندسة الطبية الحيوية</p>
                            <p class="mb-2">© 2025 - جميع الحقوق محفوظة</p>
                            <div class="flex justify-center items-center gap-4 mt-4">
                                <a href="mailto:<EMAIL>" class="text-yellow-300 hover:text-yellow-400 transition-colors">
                                    <i class="fas fa-envelope ml-1"></i>
                                    <EMAIL>
                                </a>
                            </div>
                            <div class="flex justify-center items-center gap-4 mt-2">
                                <span class="text-yellow-300">
                                    <i class="fas fa-phone ml-1"></i>
                                    +249912867327
                                </span>
                                <span class="text-yellow-300">
                                    <i class="fas fa-phone ml-1"></i>
                                    +966538076790
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="JS/story-resources.js"></script>
    <script src="JS/character-environment-library.js"></script>
    <script src="JS/educational-lessons-system.js"></script>
    <script src="JS/story-endings-system.js"></script>
    <script src="JS/advanced-dialogue-system.js"></script>
    <script src="JS/dynamic-characters-system.js"></script>
    <script src="JS/app.js"></script>
    <script src="JS/story-result.js"></script>
</body>
</html>
