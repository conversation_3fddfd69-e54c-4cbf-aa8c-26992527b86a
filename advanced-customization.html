<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 التخصيص المتقدم - صانع القصص التفاعلي للأطفال 📖</title>
    <meta name="description" content="أدوات تخصيص متقدمة لإنشاء قصص مخصصة بدقة عالية مع خيارات شاملة للنبرة والتعقيد والطول">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Scheherazade+New:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="CSS/styles.css">
    
    <style>
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .customization-card {
            transition: all 0.3s ease;
            transform: translateY(0);
        }
        
        .customization-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .option-selector {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option-selector:hover {
            transform: scale(1.05);
        }
        
        .option-selector.selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.1);
        }
        
        .slider-container {
            position: relative;
            margin: 20px 0;
        }
        
        .slider {
            width: 100%;
            height: 8px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
            opacity: 0.7;
            transition: opacity 0.2s;
        }
        
        .slider:hover {
            opacity: 1;
        }
        
        .slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }
        
        .preview-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .enhanced-preview {
            position: relative;
            overflow: hidden;
        }

        .enhanced-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
            pointer-events: none;
        }

        .preview-header {
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }

        .preview-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .preview-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .preview-card:hover::before {
            left: 100%;
        }

        .preview-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .preview-card.selected {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }

        .preview-card.selected .preview-status i {
            color: #4CAF50 !important;
        }

        .preview-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            flex-shrink: 0;
        }

        .tone-preview .preview-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .complexity-preview .preview-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .length-preview .preview-icon {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .elements-preview .preview-icon {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .theme-preview .preview-icon {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .preview-content {
            flex: 1;
        }

        .preview-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
            font-size: 16px;
        }

        .preview-text {
            color: #4a5568;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .preview-example {
            background: rgba(102, 126, 234, 0.1);
            padding: 8px 12px;
            border-radius: 8px;
            margin-top: 8px;
        }

        .example-label {
            font-weight: 600;
            color: #667eea;
            font-size: 12px;
        }

        .example-text {
            color: #4a5568;
            font-size: 12px;
            margin-right: 5px;
        }

        .elements-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .element-tag {
            background: #667eea;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .preview-status {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .story-preview-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }

        .story-preview-card.has-content {
            border-style: solid;
            border-color: #667eea;
        }

        .story-preview-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            font-weight: 600;
            color: #2d3748;
        }

        .story-preview-content {
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .story-preview-placeholder {
            text-align: center;
            color: #a0aec0;
        }

        .story-preview-text {
            background: #f7fafc;
            padding: 15px;
            border-radius: 10px;
            border-right: 4px solid #667eea;
            font-style: italic;
            color: #2d3748;
            line-height: 1.6;
        }

        .progress-indicator {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .progress-title {
            font-weight: 600;
            color: #2d3748;
        }

        .progress-percentage {
            font-weight: 700;
            color: #667eea;
            font-size: 18px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.5s ease;
            width: 0%;
        }

        .progress-items {
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }

        .progress-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #4a5568;
            transition: color 0.3s ease;
        }

        .progress-item.completed {
            color: #4CAF50;
        }

        .progress-item.completed i {
            color: #4CAF50;
        }

        .progress-item i {
            font-size: 8px;
            color: #cbd5e0;
            transition: color 0.3s ease;
        }

        .action-btn {
            width: 100%;
            padding: 15px 20px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .primary-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .primary-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .primary-btn:disabled {
            background: #cbd5e0;
            color: #a0aec0;
            cursor: not-allowed;
            box-shadow: none;
        }

        .secondary-btn {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
        }

        .secondary-btn:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }

        .tertiary-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .tertiary-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        }

        .btn-glow {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }

        .primary-btn:hover:not(:disabled) .btn-glow {
            left: 100%;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
    <!-- Header -->
    <header class="glass-effect py-6 mb-8">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <i class="fas fa-cogs text-3xl text-white"></i>
                    <h1 class="text-2xl md:text-3xl font-bold text-white arabic-text">
                        🎨 التخصيص المتقدم للقصص 🎨
                    </h1>
                </div>
                <a href="index.html" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-2 rounded-full transition-all duration-300">
                    <i class="fas fa-home ml-2"></i>
                    العودة للرئيسية
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mx-auto px-4 pb-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Customization Panel -->
            <div class="lg:col-span-2 space-y-6">
                
                <!-- Story Tone Selection -->
                <div class="customization-card glass-effect rounded-2xl p-6">
                    <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-volume-up ml-3 text-yellow-400"></i>
                        نبرة القصة
                    </h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="option-selector glass-effect rounded-xl p-4 text-center" data-tone="gentle">
                            <i class="fas fa-moon text-2xl text-blue-300 mb-2"></i>
                            <h3 class="text-white font-semibold">هادئ ولطيف</h3>
                            <p class="text-gray-300 text-sm">مناسب لوقت النوم</p>
                        </div>
                        <div class="option-selector glass-effect rounded-xl p-4 text-center" data-tone="exciting">
                            <i class="fas fa-bolt text-2xl text-yellow-400 mb-2"></i>
                            <h3 class="text-white font-semibold">مثير ومشوق</h3>
                            <p class="text-gray-300 text-sm">مليء بالحماس</p>
                        </div>
                        <div class="option-selector glass-effect rounded-xl p-4 text-center" data-tone="educational">
                            <i class="fas fa-graduation-cap text-2xl text-green-400 mb-2"></i>
                            <h3 class="text-white font-semibold">تعليمي وثقافي</h3>
                            <p class="text-gray-300 text-sm">يركز على التعلم</p>
                        </div>
                        <div class="option-selector glass-effect rounded-xl p-4 text-center" data-tone="funny">
                            <i class="fas fa-laugh text-2xl text-pink-400 mb-2"></i>
                            <h3 class="text-white font-semibold">مرح وكوميدي</h3>
                            <p class="text-gray-300 text-sm">مليء بالضحك</p>
                        </div>
                    </div>
                </div>

                <!-- Complexity Level -->
                <div class="customization-card glass-effect rounded-2xl p-6">
                    <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-layer-group ml-3 text-purple-400"></i>
                        مستوى التعقيد
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="option-selector glass-effect rounded-xl p-4 text-center" data-complexity="simple">
                            <i class="fas fa-baby text-2xl text-green-400 mb-2"></i>
                            <h3 class="text-white font-semibold">بسيط</h3>
                            <p class="text-gray-300 text-sm">3-5 سنوات</p>
                            <p class="text-gray-400 text-xs">جمل قصيرة وكلمات بسيطة</p>
                        </div>
                        <div class="option-selector glass-effect rounded-xl p-4 text-center" data-complexity="medium">
                            <i class="fas fa-child text-2xl text-blue-400 mb-2"></i>
                            <h3 class="text-white font-semibold">متوسط</h3>
                            <p class="text-gray-300 text-sm">6-8 سنوات</p>
                            <p class="text-gray-400 text-xs">جمل متوسطة ومفردات متنوعة</p>
                        </div>
                        <div class="option-selector glass-effect rounded-xl p-4 text-center" data-complexity="advanced">
                            <i class="fas fa-user-graduate text-2xl text-purple-400 mb-2"></i>
                            <h3 class="text-white font-semibold">متقدم</h3>
                            <p class="text-gray-300 text-sm">9-12 سنة</p>
                            <p class="text-gray-400 text-xs">جمل معقدة ومفردات غنية</p>
                        </div>
                    </div>
                </div>

                <!-- Story Length -->
                <div class="customization-card glass-effect rounded-2xl p-6">
                    <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-clock ml-3 text-orange-400"></i>
                        طول القصة
                    </h2>
                    <div class="slider-container">
                        <input type="range" min="1" max="3" value="2" class="slider" id="storyLength">
                        <div class="flex justify-between text-white text-sm mt-2">
                            <span>قصيرة (3-5 دقائق)</span>
                            <span>متوسطة (5-8 دقائق)</span>
                            <span>طويلة (8-12 دقيقة)</span>
                        </div>
                    </div>
                    <div id="lengthDisplay" class="text-center text-white mt-4 p-3 glass-effect rounded-lg">
                        <i class="fas fa-info-circle ml-2"></i>
                        <span>متوسطة: 250-400 كلمة، مناسبة للقراءة اليومية</span>
                    </div>
                </div>

                <!-- Interactive Elements -->
                <div class="customization-card glass-effect rounded-2xl p-6">
                    <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-hand-pointer ml-3 text-cyan-400"></i>
                        العناصر التفاعلية
                    </h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <label class="option-selector glass-effect rounded-xl p-4 text-center cursor-pointer">
                            <input type="checkbox" class="hidden" data-element="choices">
                            <i class="fas fa-route text-2xl text-blue-400 mb-2"></i>
                            <h3 class="text-white font-semibold text-sm">خيارات القرار</h3>
                        </label>
                        <label class="option-selector glass-effect rounded-xl p-4 text-center cursor-pointer">
                            <input type="checkbox" class="hidden" data-element="questions">
                            <i class="fas fa-question-circle text-2xl text-green-400 mb-2"></i>
                            <h3 class="text-white font-semibold text-sm">أسئلة تفاعلية</h3>
                        </label>
                        <label class="option-selector glass-effect rounded-xl p-4 text-center cursor-pointer">
                            <input type="checkbox" class="hidden" data-element="sounds">
                            <i class="fas fa-music text-2xl text-yellow-400 mb-2"></i>
                            <h3 class="text-white font-semibold text-sm">أصوات ومؤثرات</h3>
                        </label>
                        <label class="option-selector glass-effect rounded-xl p-4 text-center cursor-pointer">
                            <input type="checkbox" class="hidden" data-element="actions">
                            <i class="fas fa-running text-2xl text-pink-400 mb-2"></i>
                            <h3 class="text-white font-semibold text-sm">حركات وأفعال</h3>
                        </label>
                    </div>
                </div>

                <!-- Specialized Themes -->
                <div class="customization-card glass-effect rounded-2xl p-6">
                    <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-palette ml-3 text-indigo-400"></i>
                        المواضيع المتخصصة
                    </h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="option-selector glass-effect rounded-xl p-4 text-center" data-theme="science">
                            <i class="fas fa-flask text-2xl text-cyan-400 mb-2"></i>
                            <h3 class="text-white font-semibold">العلوم</h3>
                            <p class="text-gray-300 text-xs">الفضاء والطبيعة</p>
                        </div>
                        <div class="option-selector glass-effect rounded-xl p-4 text-center" data-theme="culture">
                            <i class="fas fa-mosque text-2xl text-amber-400 mb-2"></i>
                            <h3 class="text-white font-semibold">التراث</h3>
                            <p class="text-gray-300 text-xs">الثقافة العربية</p>
                        </div>
                        <div class="option-selector glass-effect rounded-xl p-4 text-center" data-theme="values">
                            <i class="fas fa-heart text-2xl text-red-400 mb-2"></i>
                            <h3 class="text-white font-semibold">القيم</h3>
                            <p class="text-gray-300 text-xs">الأخلاق والمبادئ</p>
                        </div>
                        <div class="option-selector glass-effect rounded-xl p-4 text-center" data-theme="creativity">
                            <i class="fas fa-lightbulb text-2xl text-yellow-400 mb-2"></i>
                            <h3 class="text-white font-semibold">الإبداع</h3>
                            <p class="text-gray-300 text-xs">الفن والخيال</p>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Preview Panel -->
            <div class="lg:col-span-1">
                <div class="sticky top-6">
                    <div class="preview-panel enhanced-preview">
                        <div class="preview-header">
                            <h3 class="text-xl font-bold text-white mb-2 flex items-center">
                                <i class="fas fa-magic ml-3 text-yellow-300 animate-pulse"></i>
                                معاينة القصة المخصصة
                            </h3>
                            <p class="text-blue-100 text-sm">شاهد كيف ستبدو قصتك</p>
                        </div>

                        <div id="previewContent" class="space-y-4 mt-6">
                            <!-- Tone Preview -->
                            <div class="preview-card tone-preview" id="tonePreviewCard">
                                <div class="preview-icon">
                                    <i class="fas fa-music text-purple-500"></i>
                                </div>
                                <div class="preview-content">
                                    <h4 class="preview-title">النبرة والأسلوب</h4>
                                    <p id="selectedTone" class="preview-text">اختر النبرة المناسبة</p>
                                    <div id="toneExample" class="preview-example hidden">
                                        <span class="example-label">مثال:</span>
                                        <span id="toneExampleText" class="example-text"></span>
                                    </div>
                                </div>
                                <div class="preview-status" id="toneStatus">
                                    <i class="fas fa-circle text-gray-400"></i>
                                </div>
                            </div>

                            <!-- Complexity Preview -->
                            <div class="preview-card complexity-preview" id="complexityPreviewCard">
                                <div class="preview-icon">
                                    <i class="fas fa-brain text-green-500"></i>
                                </div>
                                <div class="preview-content">
                                    <h4 class="preview-title">مستوى التعقيد</h4>
                                    <p id="selectedComplexity" class="preview-text">اختر المستوى المناسب للعمر</p>
                                    <div id="complexityExample" class="preview-example hidden">
                                        <span class="example-label">مناسب لـ:</span>
                                        <span id="complexityExampleText" class="example-text"></span>
                                    </div>
                                </div>
                                <div class="preview-status" id="complexityStatus">
                                    <i class="fas fa-circle text-gray-400"></i>
                                </div>
                            </div>

                            <!-- Length Preview -->
                            <div class="preview-card length-preview" id="lengthPreviewCard">
                                <div class="preview-icon">
                                    <i class="fas fa-clock text-blue-500"></i>
                                </div>
                                <div class="preview-content">
                                    <h4 class="preview-title">طول القصة</h4>
                                    <p id="selectedLength" class="preview-text">متوسطة (5-8 دقائق)</p>
                                    <div id="lengthExample" class="preview-example">
                                        <span class="example-label">وقت القراءة:</span>
                                        <span id="lengthExampleText" class="example-text">مناسب للقراءة اليومية</span>
                                    </div>
                                </div>
                                <div class="preview-status" id="lengthStatus">
                                    <i class="fas fa-check-circle text-green-500"></i>
                                </div>
                            </div>

                            <!-- Interactive Elements Preview -->
                            <div class="preview-card elements-preview" id="elementsPreviewCard">
                                <div class="preview-icon">
                                    <i class="fas fa-gamepad text-orange-500"></i>
                                </div>
                                <div class="preview-content">
                                    <h4 class="preview-title">العناصر التفاعلية</h4>
                                    <p id="selectedElements" class="preview-text">اختر العناصر المناسبة</p>
                                    <div id="elementsExample" class="preview-example hidden">
                                        <div id="elementsExampleText" class="elements-grid"></div>
                                    </div>
                                </div>
                                <div class="preview-status" id="elementsStatus">
                                    <i class="fas fa-circle text-gray-400"></i>
                                </div>
                            </div>

                            <!-- Theme Preview -->
                            <div class="preview-card theme-preview" id="themePreviewCard">
                                <div class="preview-icon">
                                    <i class="fas fa-palette text-pink-500"></i>
                                </div>
                                <div class="preview-content">
                                    <h4 class="preview-title">الموضوع المتخصص</h4>
                                    <p id="selectedTheme" class="preview-text">اختر موضوعاً مثيراً</p>
                                    <div id="themeExample" class="preview-example hidden">
                                        <span class="example-label">سيتضمن:</span>
                                        <span id="themeExampleText" class="example-text"></span>
                                    </div>
                                </div>
                                <div class="preview-status" id="themeStatus">
                                    <i class="fas fa-circle text-gray-400"></i>
                                </div>
                            </div>

                            <!-- Story Preview -->
                            <div class="story-preview-card" id="storyPreviewCard">
                                <div class="story-preview-header">
                                    <i class="fas fa-book-open text-yellow-400"></i>
                                    <span>معاينة القصة</span>
                                </div>
                                <div class="story-preview-content" id="storyPreviewContent">
                                    <div class="story-preview-placeholder">
                                        <i class="fas fa-magic text-4xl text-gray-400 mb-3"></i>
                                        <p class="text-gray-500">اختر الإعدادات لرؤية معاينة القصة</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Progress Indicator -->
                            <div class="progress-indicator">
                                <div class="progress-header">
                                    <span class="progress-title">مستوى الإكمال</span>
                                    <span class="progress-percentage" id="progressPercentage">0%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progressFill"></div>
                                </div>
                                <div class="progress-items">
                                    <div class="progress-item" id="progressTone">
                                        <i class="fas fa-circle"></i>
                                        <span>النبرة</span>
                                    </div>
                                    <div class="progress-item" id="progressComplexity">
                                        <i class="fas fa-circle"></i>
                                        <span>التعقيد</span>
                                    </div>
                                    <div class="progress-item" id="progressTheme">
                                        <i class="fas fa-circle"></i>
                                        <span>الموضوع</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-6 space-y-3">
                            <button id="applySettings" class="action-btn primary-btn" disabled>
                                <i class="fas fa-rocket ml-2"></i>
                                <span>إنشاء القصة المخصصة</span>
                                <div class="btn-glow"></div>
                            </button>

                            <button id="resetSettings" class="action-btn secondary-btn">
                                <i class="fas fa-undo ml-2"></i>
                                <span>إعادة تعيين الإعدادات</span>
                            </button>

                            <button id="savePreset" class="action-btn tertiary-btn">
                                <i class="fas fa-save ml-2"></i>
                                <span>حفظ كإعداد مفضل</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="JS/story-resources.js"></script>
    <script src="JS/advanced-customization.js"></script>
</body>
</html>
