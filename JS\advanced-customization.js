// 🎨 نظام التخصيص المتقدم للقصص 🎨
// Advanced Story Customization System
// Dr.<PERSON>, SUST -BME, @ 2025

class AdvancedCustomization {
    constructor() {
        this.settings = {
            tone: null,
            complexity: null,
            length: 2, // متوسطة افتراضياً
            interactiveElements: [],
            theme: null
        };
        
        this.storyResources = new StoryResources();
        this.initializeEventListeners();
        this.updatePreview();
    }

    initializeEventListeners() {
        // مستمعي الأحداث لاختيار النبرة
        document.querySelectorAll('[data-tone]').forEach(element => {
            element.addEventListener('click', (e) => {
                this.selectTone(e.currentTarget.dataset.tone);
            });
        });

        // مستمعي الأحداث لاختيار مستوى التعقيد
        document.querySelectorAll('[data-complexity]').forEach(element => {
            element.addEventListener('click', (e) => {
                this.selectComplexity(e.currentTarget.dataset.complexity);
            });
        });

        // مستمع الحدث لشريط تمرير طول القصة
        const lengthSlider = document.getElementById('storyLength');
        lengthSlider.addEventListener('input', (e) => {
            this.updateStoryLength(parseInt(e.target.value));
        });

        // مستمعي الأحداث للعناصر التفاعلية
        document.querySelectorAll('[data-element]').forEach(element => {
            element.addEventListener('change', (e) => {
                this.toggleInteractiveElement(e.target.dataset.element, e.target.checked);
            });
        });

        // مستمعي الأحداث للمواضيع المتخصصة
        document.querySelectorAll('[data-theme]').forEach(element => {
            element.addEventListener('click', (e) => {
                this.selectTheme(e.currentTarget.dataset.theme);
            });
        });

        // أزرار التحكم
        document.getElementById('applySettings').addEventListener('click', () => {
            this.applySettings();
        });

        document.getElementById('resetSettings').addEventListener('click', () => {
            this.resetSettings();
        });

        document.getElementById('savePreset').addEventListener('click', () => {
            this.savePreset();
        });

        // إضافة تأثيرات تفاعلية للبطاقات
        this.addCardInteractions();
    }

    selectTone(tone) {
        // إزالة التحديد السابق
        document.querySelectorAll('[data-tone]').forEach(el => {
            el.classList.remove('selected');
        });

        // تحديد النبرة الجديدة
        document.querySelector(`[data-tone="${tone}"]`).classList.add('selected');
        this.settings.tone = tone;
        this.updatePreview();
    }

    selectComplexity(complexity) {
        // إزالة التحديد السابق
        document.querySelectorAll('[data-complexity]').forEach(el => {
            el.classList.remove('selected');
        });

        // تحديد مستوى التعقيد الجديد
        document.querySelector(`[data-complexity="${complexity}"]`).classList.add('selected');
        this.settings.complexity = complexity;
        this.updatePreview();
    }

    updateStoryLength(length) {
        this.settings.length = length;
        
        const lengthTexts = {
            1: 'قصيرة: 150-250 كلمة، مناسبة لوقت النوم السريع',
            2: 'متوسطة: 250-400 كلمة، مناسبة للقراءة اليومية',
            3: 'طويلة: 400-600 كلمة، مناسبة لعطلة نهاية الأسبوع'
        };

        document.getElementById('lengthDisplay').innerHTML = `
            <i class="fas fa-info-circle ml-2"></i>
            <span>${lengthTexts[length]}</span>
        `;

        this.updatePreview();
    }

    toggleInteractiveElement(element, isChecked) {
        if (isChecked) {
            if (!this.settings.interactiveElements.includes(element)) {
                this.settings.interactiveElements.push(element);
            }
            // إضافة تأثير بصري للتحديد
            document.querySelector(`[data-element="${element}"]`).parentElement.classList.add('selected');
        } else {
            this.settings.interactiveElements = this.settings.interactiveElements.filter(el => el !== element);
            // إزالة التأثير البصري
            document.querySelector(`[data-element="${element}"]`).parentElement.classList.remove('selected');
        }
        this.updatePreview();
    }

    selectTheme(theme) {
        // إزالة التحديد السابق
        document.querySelectorAll('[data-theme]').forEach(el => {
            el.classList.remove('selected');
        });

        // تحديد الموضوع الجديد
        document.querySelector(`[data-theme="${theme}"]`).classList.add('selected');
        this.settings.theme = theme;
        this.updatePreview();
    }

    updatePreview() {
        // تحديث معاينة النبرة
        this.updateTonePreview();

        // تحديث معاينة مستوى التعقيد
        this.updateComplexityPreview();

        // تحديث معاينة طول القصة
        this.updateLengthPreview();

        // تحديث معاينة العناصر التفاعلية
        this.updateElementsPreview();

        // تحديث معاينة الموضوع
        this.updateThemePreview();

        // تحديث معاينة القصة
        this.updateStoryPreview();

        // تحديث مؤشر التقدم
        this.updateProgressIndicator();

        // تحديث حالة الأزرار
        this.updateButtonStates();
    }

    updateTonePreview() {
        const toneNames = {
            gentle: 'هادئ ولطيف',
            exciting: 'مثير ومشوق',
            educational: 'تعليمي وثقافي',
            funny: 'مرح وكوميدي'
        };

        const toneExamples = {
            gentle: '"كان يا ما كان، في ليلة هادئة جميلة..."',
            exciting: '"واااو! مغامرة رائعة تنتظرنا!"',
            educational: '"هل تعلم أن النجوم تضيء في السماء لتعلمنا..."',
            funny: '"هههههه! ما أطرف هذا الموقف!"'
        };

        const toneCard = document.getElementById('tonePreviewCard');
        const toneText = document.getElementById('selectedTone');
        const toneExample = document.getElementById('toneExample');
        const toneExampleText = document.getElementById('toneExampleText');
        const toneStatus = document.getElementById('toneStatus');

        if (this.settings.tone) {
            toneText.textContent = toneNames[this.settings.tone];
            toneExampleText.textContent = toneExamples[this.settings.tone];
            toneExample.classList.remove('hidden');
            toneCard.classList.add('selected');
            toneStatus.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
        } else {
            toneText.textContent = 'اختر النبرة المناسبة';
            toneExample.classList.add('hidden');
            toneCard.classList.remove('selected');
            toneStatus.innerHTML = '<i class="fas fa-circle text-gray-400"></i>';
        }
    }

    updateComplexityPreview() {
        const complexityNames = {
            simple: 'بسيط',
            medium: 'متوسط',
            advanced: 'متقدم'
        };

        const complexityAges = {
            simple: 'الأطفال الصغار (3-5 سنوات)',
            medium: 'الأطفال المتوسطين (6-8 سنوات)',
            advanced: 'الأطفال الأكبر (9-12 سنة)'
        };

        const complexityCard = document.getElementById('complexityPreviewCard');
        const complexityText = document.getElementById('selectedComplexity');
        const complexityExample = document.getElementById('complexityExample');
        const complexityExampleText = document.getElementById('complexityExampleText');
        const complexityStatus = document.getElementById('complexityStatus');

        if (this.settings.complexity) {
            complexityText.textContent = complexityNames[this.settings.complexity];
            complexityExampleText.textContent = complexityAges[this.settings.complexity];
            complexityExample.classList.remove('hidden');
            complexityCard.classList.add('selected');
            complexityStatus.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
        } else {
            complexityText.textContent = 'اختر المستوى المناسب للعمر';
            complexityExample.classList.add('hidden');
            complexityCard.classList.remove('selected');
            complexityStatus.innerHTML = '<i class="fas fa-circle text-gray-400"></i>';
        }
    }

    updateLengthPreview() {
        const lengthNames = {
            1: 'قصيرة',
            2: 'متوسطة',
            3: 'طويلة'
        };

        const lengthTimes = {
            1: '3-5 دقائق',
            2: '5-8 دقائق',
            3: '8-12 دقيقة'
        };

        const lengthText = document.getElementById('selectedLength');
        const lengthExampleText = document.getElementById('lengthExampleText');

        lengthText.textContent = `${lengthNames[this.settings.length]} (${lengthTimes[this.settings.length]})`;
        lengthExampleText.textContent = this.getLengthDescription(this.settings.length);
    }

    getLengthDescription(length) {
        const descriptions = {
            1: 'مناسبة لوقت النوم السريع',
            2: 'مناسبة للقراءة اليومية',
            3: 'مناسبة لعطلة نهاية الأسبوع'
        };
        return descriptions[length];
    }

    updateElementsPreview() {
        const elementNames = {
            choices: 'خيارات القرار',
            questions: 'أسئلة تفاعلية',
            sounds: 'أصوات ومؤثرات',
            actions: 'حركات وأفعال'
        };

        const elementsCard = document.getElementById('elementsPreviewCard');
        const elementsText = document.getElementById('selectedElements');
        const elementsExample = document.getElementById('elementsExample');
        const elementsExampleText = document.getElementById('elementsExampleText');
        const elementsStatus = document.getElementById('elementsStatus');

        if (this.settings.interactiveElements.length > 0) {
            elementsText.textContent = `${this.settings.interactiveElements.length} عنصر محدد`;

            // إنشاء عرض العناصر
            elementsExampleText.innerHTML = this.settings.interactiveElements
                .map(el => `<span class="element-tag">${elementNames[el]}</span>`)
                .join('');

            elementsExample.classList.remove('hidden');
            elementsCard.classList.add('selected');
            elementsStatus.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
        } else {
            elementsText.textContent = 'اختر العناصر المناسبة';
            elementsExample.classList.add('hidden');
            elementsCard.classList.remove('selected');
            elementsStatus.innerHTML = '<i class="fas fa-circle text-gray-400"></i>';
        }
    }

    updateThemePreview() {
        const themeNames = {
            science: 'العلوم والاكتشاف',
            culture: 'الثقافة والتراث',
            values: 'القيم والأخلاق',
            creativity: 'الإبداع والخيال'
        };

        const themeDescriptions = {
            science: 'الفضاء، الطبيعة، التجارب العلمية',
            culture: 'التراث العربي، العادات، التاريخ',
            values: 'المبادئ الأخلاقية، القيم النبيلة',
            creativity: 'الفن، الابتكار، الخيال الواسع'
        };

        const themeCard = document.getElementById('themePreviewCard');
        const themeText = document.getElementById('selectedTheme');
        const themeExample = document.getElementById('themeExample');
        const themeExampleText = document.getElementById('themeExampleText');
        const themeStatus = document.getElementById('themeStatus');

        if (this.settings.theme) {
            themeText.textContent = themeNames[this.settings.theme];
            themeExampleText.textContent = themeDescriptions[this.settings.theme];
            themeExample.classList.remove('hidden');
            themeCard.classList.add('selected');
            themeStatus.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
        } else {
            themeText.textContent = 'اختر موضوعاً مثيراً';
            themeExample.classList.add('hidden');
            themeCard.classList.remove('selected');
            themeStatus.innerHTML = '<i class="fas fa-circle text-gray-400"></i>';
        }
    }

    updateStoryPreview() {
        const storyCard = document.getElementById('storyPreviewCard');
        const storyContent = document.getElementById('storyPreviewContent');

        if (this.settings.tone && this.settings.complexity) {
            const previewText = this.generateStoryPreview();
            storyContent.innerHTML = `<div class="story-preview-text">${previewText}</div>`;
            storyCard.classList.add('has-content');
        } else {
            storyContent.innerHTML = `
                <div class="story-preview-placeholder">
                    <i class="fas fa-magic text-4xl text-gray-400 mb-3"></i>
                    <p class="text-gray-500">اختر النبرة والتعقيد لرؤية معاينة القصة</p>
                </div>
            `;
            storyCard.classList.remove('has-content');
        }
    }

    generateStoryPreview() {
        const toneStyles = {
            gentle: 'كان يا ما كان، في مكان هادئ وجميل...',
            exciting: 'في يوم مليء بالمغامرات المثيرة...',
            educational: 'هل تعلم أن هناك عالماً مليئاً بالأسرار...',
            funny: 'في قصة مضحكة ومرحة...'
        };

        const complexityStyles = {
            simple: 'سنتعلم معاً أشياء جميلة وبسيطة.',
            medium: 'سنكتشف أسراراً رائعة ونتعلم دروساً مفيدة.',
            advanced: 'سنخوض رحلة عميقة لاستكشاف المعاني والحكم.'
        };

        return `${toneStyles[this.settings.tone]} ${complexityStyles[this.settings.complexity]}`;
    }

    updateProgressIndicator() {
        const requiredSettings = ['tone', 'complexity', 'theme'];
        const completedSettings = requiredSettings.filter(setting => this.settings[setting]);
        const progressPercentage = Math.round((completedSettings.length / requiredSettings.length) * 100);

        // تحديث النسبة المئوية
        document.getElementById('progressPercentage').textContent = `${progressPercentage}%`;

        // تحديث شريط التقدم
        document.getElementById('progressFill').style.width = `${progressPercentage}%`;

        // تحديث عناصر التقدم
        document.getElementById('progressTone').classList.toggle('completed', this.settings.tone);
        document.getElementById('progressComplexity').classList.toggle('completed', this.settings.complexity);
        document.getElementById('progressTheme').classList.toggle('completed', this.settings.theme);
    }

    updateButtonStates() {
        const applyButton = document.getElementById('applySettings');
        const isComplete = this.settings.tone && this.settings.complexity;

        applyButton.disabled = !isComplete;

        if (isComplete) {
            applyButton.innerHTML = `
                <i class="fas fa-rocket ml-2"></i>
                <span>إنشاء القصة المخصصة</span>
                <div class="btn-glow"></div>
            `;
        } else {
            applyButton.innerHTML = `
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <span>اختر النبرة والتعقيد أولاً</span>
            `;
        }
    }

    applySettings() {
        // التحقق من اكتمال الإعدادات الأساسية
        if (!this.settings.tone || !this.settings.complexity) {
            this.showNotification('يرجى اختيار النبرة ومستوى التعقيد على الأقل', 'warning');
            return;
        }

        // حفظ الإعدادات في التخزين المحلي
        localStorage.setItem('advancedStorySettings', JSON.stringify(this.settings));
        
        // إظهار رسالة نجاح
        this.showNotification('تم حفظ الإعدادات بنجاح! يمكنك الآن إنشاء قصة مخصصة.', 'success');
        
        // إعادة توجيه إلى صفحة إنشاء القصة بعد ثانيتين
        setTimeout(() => {
            window.location.href = 'interactive-story-maker.html';
        }, 2000);
    }

    resetSettings() {
        // إعادة تعيين جميع الإعدادات
        this.settings = {
            tone: null,
            complexity: null,
            length: 2,
            interactiveElements: [],
            theme: null
        };

        // إزالة جميع التحديدات البصرية
        document.querySelectorAll('.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // إعادة تعيين شريط التمرير
        document.getElementById('storyLength').value = 2;
        this.updateStoryLength(2);

        // إعادة تعيين العناصر التفاعلية
        document.querySelectorAll('[data-element]').forEach(checkbox => {
            checkbox.checked = false;
        });

        // تحديث المعاينة
        this.updatePreview();
        
        // إظهار رسالة
        this.showNotification('تم إعادة تعيين جميع الإعدادات', 'info');
    }

    showNotification(message, type = 'info') {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
        
        // تحديد لون الإشعار حسب النوع
        const colors = {
            success: 'bg-green-500 text-white',
            warning: 'bg-yellow-500 text-black',
            error: 'bg-red-500 text-white',
            info: 'bg-blue-500 text-white'
        };
        
        notification.className += ` ${colors[type]}`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation-triangle' : type === 'error' ? 'times' : 'info-circle'} ml-2"></i>
                <span>${message}</span>
            </div>
        `;

        // إضافة الإشعار إلى الصفحة
        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // إخفاء الإشعار بعد 4 ثوانٍ
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 4000);
    }

    // دالة للحصول على الإعدادات المحفوظة
    static getSavedSettings() {
        const saved = localStorage.getItem('advancedStorySettings');
        return saved ? JSON.parse(saved) : null;
    }

    // دالة لتطبيق الإعدادات على القصة
    static applySettingsToStory(storyContent, settings) {
        if (!settings) return storyContent;

        let enhancedContent = storyContent;

        // تطبيق النبرة
        if (settings.tone) {
            enhancedContent = this.applyTone(enhancedContent, settings.tone);
        }

        // تطبيق مستوى التعقيد
        if (settings.complexity) {
            enhancedContent = this.applyComplexity(enhancedContent, settings.complexity);
        }

        // تطبيق طول القصة
        enhancedContent = this.adjustLength(enhancedContent, settings.length);

        // إضافة العناصر التفاعلية
        if (settings.interactiveElements.length > 0) {
            enhancedContent = this.addInteractiveElements(enhancedContent, settings.interactiveElements);
        }

        return enhancedContent;
    }

    static applyTone(content, tone) {
        // تطبيق النبرة على المحتوى
        const toneModifiers = {
            gentle: content => content.replace(/!/g, '.').replace(/\?/g, '؟'),
            exciting: content => content.replace(/\./g, '!').replace(/،/g, '، وفجأة'),
            educational: content => content.replace(/كان/g, 'كان من المعروف أن'),
            funny: content => content.replace(/قال/g, 'قال بمرح').replace(/فعل/g, 'فعل بطريقة مضحكة')
        };

        return toneModifiers[tone] ? toneModifiers[tone](content) : content;
    }

    static applyComplexity(content, complexity) {
        // تطبيق مستوى التعقيد
        if (complexity === 'simple') {
            return content.replace(/الذي|التي/g, '').replace(/حيث/g, 'وقت ما');
        } else if (complexity === 'advanced') {
            return content.replace(/قال/g, 'صرح بوضوح').replace(/ذهب/g, 'توجه بخطوات واثقة');
        }
        return content;
    }

    static adjustLength(content, length) {
        // تعديل طول القصة
        const sentences = content.split('.');
        const targetLengths = { 1: 8, 2: 12, 3: 18 };
        const targetLength = targetLengths[length];

        if (sentences.length > targetLength) {
            return sentences.slice(0, targetLength).join('.') + '.';
        } else if (sentences.length < targetLength) {
            // إضافة جمل وصفية
            const additionalSentences = [
                ' كانت الشمس تشرق بدفء',
                ' والطيور تغرد بجمال',
                ' والأزهار تفوح بعطر رائع',
                ' والهواء منعش ولطيف'
            ];
            
            while (sentences.length < targetLength && additionalSentences.length > 0) {
                const randomSentence = additionalSentences.splice(Math.floor(Math.random() * additionalSentences.length), 1)[0];
                sentences.splice(Math.floor(sentences.length / 2), 0, randomSentence);
            }
        }

        return sentences.join('.');
    }

    static addInteractiveElements(content, elements) {
        // إضافة العناصر التفاعلية
        let enhancedContent = content;

        if (elements.includes('questions')) {
            enhancedContent += '\n\n🤔 ماذا تعتقد أن البطل سيفعل بعد ذلك؟';
        }

        if (elements.includes('sounds')) {
            enhancedContent = enhancedContent.replace(/طائر/g, 'طائر يغرد "تغريد تغريد"');
            enhancedContent = enhancedContent.replace(/ماء/g, 'ماء يجري "خرير خرير"');
        }

        if (elements.includes('actions')) {
            enhancedContent += '\n\n🤸‍♀️ يمكنك أن تقلد حركات البطل وأنت تستمع للقصة!';
        }

        if (elements.includes('choices')) {
            enhancedContent += '\n\n🔀 اختر: هل تريد أن يذهب البطل إلى الغابة أم إلى الشاطئ؟';
        }

        return enhancedContent;
    }

    // ===== دوال التفاعل الإضافية =====

    addCardInteractions() {
        // إضافة تأثيرات hover للبطاقات
        const cards = document.querySelectorAll('.preview-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                this.animateCard(card, 'enter');
            });

            card.addEventListener('mouseleave', () => {
                this.animateCard(card, 'leave');
            });
        });

        // إضافة تأثيرات للأزرار
        const buttons = document.querySelectorAll('.action-btn');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', () => {
                this.animateButton(button, 'enter');
            });

            button.addEventListener('mouseleave', () => {
                this.animateButton(button, 'leave');
            });
        });
    }

    animateCard(card, action) {
        if (action === 'enter') {
            card.style.transform = 'translateY(-3px) scale(1.02)';
            card.style.boxShadow = '0 15px 35px rgba(0,0,0,0.2)';
        } else {
            card.style.transform = 'translateY(0) scale(1)';
            card.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
        }
    }

    animateButton(button, action) {
        if (action === 'enter' && !button.disabled) {
            button.style.transform = 'translateY(-2px)';
        } else {
            button.style.transform = 'translateY(0)';
        }
    }

    savePreset() {
        if (!this.settings.tone || !this.settings.complexity) {
            this.showNotification('يرجى اختيار النبرة ومستوى التعقيد على الأقل لحفظ الإعداد', 'warning');
            return;
        }

        // طلب اسم للإعداد المفضل
        const presetName = prompt('أدخل اسماً لهذا الإعداد المفضل:');
        if (!presetName) return;

        // حفظ الإعداد
        const savedPresets = JSON.parse(localStorage.getItem('storyPresets') || '{}');
        savedPresets[presetName] = {
            ...this.settings,
            createdAt: new Date().toISOString(),
            name: presetName
        };

        localStorage.setItem('storyPresets', JSON.stringify(savedPresets));

        this.showNotification(`تم حفظ الإعداد "${presetName}" بنجاح!`, 'success');

        // إضافة زر لتحميل الإعدادات المحفوظة
        this.addPresetButton(presetName);
    }

    addPresetButton(presetName) {
        const presetsContainer = document.getElementById('presetsContainer') || this.createPresetsContainer();

        const presetButton = document.createElement('button');
        presetButton.className = 'preset-btn';
        presetButton.innerHTML = `
            <i class="fas fa-star ml-2"></i>
            <span>${presetName}</span>
            <i class="fas fa-times preset-delete" data-preset="${presetName}"></i>
        `;

        presetButton.addEventListener('click', (e) => {
            if (e.target.classList.contains('preset-delete')) {
                this.deletePreset(presetName);
                presetButton.remove();
            } else {
                this.loadPreset(presetName);
            }
        });

        presetsContainer.appendChild(presetButton);
    }

    createPresetsContainer() {
        const container = document.createElement('div');
        container.id = 'presetsContainer';
        container.className = 'presets-container';
        container.innerHTML = `
            <h4 class="presets-title">
                <i class="fas fa-star ml-2"></i>
                الإعدادات المفضلة
            </h4>
            <div class="presets-grid"></div>
        `;

        // إدراج الحاوية قبل الأزرار
        const buttonsContainer = document.querySelector('.action-btn').parentElement;
        buttonsContainer.parentElement.insertBefore(container, buttonsContainer);

        return container.querySelector('.presets-grid');
    }

    loadPreset(presetName) {
        const savedPresets = JSON.parse(localStorage.getItem('storyPresets') || '{}');
        const preset = savedPresets[presetName];

        if (!preset) {
            this.showNotification('لم يتم العثور على الإعداد المطلوب', 'error');
            return;
        }

        // تحميل الإعدادات
        this.settings = { ...preset };

        // تحديث الواجهة
        this.applyPresetToUI();
        this.updatePreview();

        this.showNotification(`تم تحميل الإعداد "${presetName}" بنجاح!`, 'success');
    }

    applyPresetToUI() {
        // تطبيق النبرة
        if (this.settings.tone) {
            document.querySelectorAll('[data-tone]').forEach(el => {
                el.classList.toggle('selected', el.dataset.tone === this.settings.tone);
            });
        }

        // تطبيق التعقيد
        if (this.settings.complexity) {
            document.querySelectorAll('[data-complexity]').forEach(el => {
                el.classList.toggle('selected', el.dataset.complexity === this.settings.complexity);
            });
        }

        // تطبيق طول القصة
        document.getElementById('storyLength').value = this.settings.length;

        // تطبيق العناصر التفاعلية
        document.querySelectorAll('[data-element]').forEach(checkbox => {
            checkbox.checked = this.settings.interactiveElements.includes(checkbox.dataset.element);
            checkbox.parentElement.classList.toggle('selected', checkbox.checked);
        });

        // تطبيق الموضوع
        if (this.settings.theme) {
            document.querySelectorAll('[data-theme]').forEach(el => {
                el.classList.toggle('selected', el.dataset.theme === this.settings.theme);
            });
        }
    }

    deletePreset(presetName) {
        if (!confirm(`هل أنت متأكد من حذف الإعداد "${presetName}"؟`)) {
            return;
        }

        const savedPresets = JSON.parse(localStorage.getItem('storyPresets') || '{}');
        delete savedPresets[presetName];
        localStorage.setItem('storyPresets', JSON.stringify(savedPresets));

        this.showNotification(`تم حذف الإعداد "${presetName}"`, 'info');
    }

    loadSavedPresets() {
        const savedPresets = JSON.parse(localStorage.getItem('storyPresets') || '{}');
        Object.keys(savedPresets).forEach(presetName => {
            this.addPresetButton(presetName);
        });
    }

    // تحسين دالة إعادة التعيين
    resetSettings() {
        // إضافة تأكيد
        if (!confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
            return;
        }

        // إعادة تعيين جميع الإعدادات مع تأثيرات بصرية
        this.animateReset();

        setTimeout(() => {
            this.settings = {
                tone: null,
                complexity: null,
                length: 2,
                interactiveElements: [],
                theme: null
            };

            // إزالة جميع التحديدات البصرية
            document.querySelectorAll('.selected').forEach(el => {
                el.classList.remove('selected');
            });

            // إعادة تعيين شريط التمرير
            document.getElementById('storyLength').value = 2;
            this.updateStoryLength(2);

            // إعادة تعيين العناصر التفاعلية
            document.querySelectorAll('[data-element]').forEach(checkbox => {
                checkbox.checked = false;
            });

            // تحديث المعاينة
            this.updatePreview();

            // إظهار رسالة
            this.showNotification('تم إعادة تعيين جميع الإعدادات بنجاح', 'info');
        }, 300);
    }

    animateReset() {
        const cards = document.querySelectorAll('.preview-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.transform = 'scale(0.95)';
                card.style.opacity = '0.5';

                setTimeout(() => {
                    card.style.transform = 'scale(1)';
                    card.style.opacity = '1';
                }, 150);
            }, index * 50);
        });
    }
}

// إضافة CSS للإعدادات المفضلة
const presetStyles = `
    .presets-container {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
    }

    .presets-title {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .presets-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
    }

    .preset-btn {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 12px 15px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        overflow: hidden;
    }

    .preset-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(240, 147, 251, 0.3);
    }

    .preset-delete {
        opacity: 0;
        transition: opacity 0.3s ease;
        padding: 5px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
    }

    .preset-btn:hover .preset-delete {
        opacity: 1;
    }

    .preset-delete:hover {
        background: rgba(255,255,255,0.3);
    }
`;

// إضافة الأنماط إلى الصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = presetStyles;
document.head.appendChild(styleSheet);

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    const customization = new AdvancedCustomization();
    customization.loadSavedPresets();
});
