<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📖 صانع القصص التفاعلي للأطفال 🎨</title>
    <meta name="description" content="اكتشفوا عالمًا سحريًا من القصص المخصصة لطفلكم! مع صانع القصص التفاعلي">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
        }
        
        .arabic-text {
            font-family: 'Tajawal', sans-serif;
        }
        
        .story-text {
            font-family: 'Scheherazade New', serif;
            line-height: 2.2;
            font-size: 1.2rem;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }
        
        .fade-in {
            animation: fadeIn 1s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .collaboration-indicator {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 3s ease infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .step-indicator {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .step-indicator.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        }
        
        .step-indicator.completed {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }
        
        .step-indicator.inactive {
            background: rgba(255, 255, 255, 0.3);
            color: #666;
        }
        
        .step-line {
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            margin: 0 10px;
            flex: 1;
            border-radius: 2px;
            opacity: 0.3;
            transition: opacity 0.3s ease;
        }
        
        .step-line.active {
            opacity: 1;
        }
        
        .collaboration-message {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            padding: 15px 20px;
            margin: 15px 0;
            color: white;
            position: relative;
            animation: slideInRight 0.5s ease-out;
        }
        
        @keyframes slideInRight {
            from { transform: translateX(100px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .user-input-area {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .user-input-area:focus-within {
            border-color: #667eea;
            box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
        }
        
        .ai-suggestion {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            border-radius: 15px;
            padding: 15px;
            margin: 10px 0;
            animation: slideInLeft 0.5s ease-out;
        }
        
        @keyframes slideInLeft {
            from { transform: translateX(-100px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .interactive-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .interactive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .interactive-button:active {
            transform: translateY(0);
        }
        
        .magic-sparkle {
            position: absolute;
            color: #ffd700;
            animation: sparkle 2s infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
        }
        
        .collaboration-workspace {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .collaboration-workspace {
                grid-template-columns: 1fr;
            }
        }
        
        .user-side {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid #4CAF50;
        }
        
        .ai-side {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid #2196F3;
        }
        
        .linguistic-accuracy-badge {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
            margin: 5px;
        }
        
        .story-preview {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            color: #333;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            border-radius: 4px;
            transition: width 0.5s ease;
        }
        
        .emoji-large {
            font-size: 2rem;
            margin: 0 10px;
        }
        
        .title-gradient {
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease infinite;
            background-size: 400% 400%;
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8">
        <!-- Header Section -->
        <header class="text-center mb-12 fade-in">
            <div class="floating-animation">
                <h1 class="text-5xl font-bold title-gradient mb-6 arabic-text">
                    📖 صانع القصص التفاعلي للأطفال 🎨
                </h1>
                <div class="collaboration-indicator text-white p-4 rounded-2xl mb-6">
                    <h2 class="text-2xl font-bold mb-3 arabic-text">
                        🪄✨ اكتشفوا عالمًا سحريًا من القصص المخصصة لطفلكم! ✨🪄
                    </h2>
                </div>
            </div>
            
            <!-- Introduction -->
            <div class="glass-effect rounded-3xl p-8 mb-8 max-w-4xl mx-auto">
                <h3 class="text-2xl font-bold text-white mb-6 arabic-text">
                    مع صانع القصص التفاعلي، يمكنكم إنشاء حكايات فريدة:
                </h3>
                <div class="grid md:grid-cols-3 gap-6 text-white">
                    <div class="text-center">
                        <div class="emoji-large">🌟</div>
                        <p class="arabic-text font-medium">تناسب عمره واهتماماته</p>
                        <p class="text-sm text-purple-200 arabic-text">(ديناصورات؟ 🦖 فضاء؟ 🚀 أميرات؟ 👑 أنتم تختارون!)</p>
                    </div>
                    <div class="text-center">
                        <div class="emoji-large">🧠</div>
                        <p class="arabic-text font-medium">تحمل دروسًا تعليمية وقيمًا جميلة</p>
                        <p class="text-sm text-purple-200 arabic-text">مثل الشجاعة والصداقة</p>
                    </div>
                    <div class="text-center">
                        <div class="emoji-large">🎨</div>
                        <p class="arabic-text font-medium">تنمي مهاراته اللغوية وإبداعه</p>
                        <p class="text-sm text-purple-200 arabic-text">وتحفز خياله</p>
                    </div>
                </div>
            </div>

            <!-- Linguistic Accuracy Badges -->
            <div class="flex flex-wrap justify-center gap-3 mb-8">
                <div class="linguistic-accuracy-badge">
                    <i class="fas fa-check-circle ml-2"></i>
                    دقة نحوية مطلقة
                </div>
                <div class="linguistic-accuracy-badge">
                    <i class="fas fa-language ml-2"></i>
                    لغة عربية فصحى سليمة
                </div>
                <div class="linguistic-accuracy-badge">
                    <i class="fas fa-balance-scale ml-2"></i>
                    مطابقة التذكير والتأنيث
                </div>
                <div class="linguistic-accuracy-badge">
                    <i class="fas fa-cogs ml-2"></i>
                    تصريف أفعال صحيح
                </div>
            </div>
        </header>

        <!-- Progress Bar -->
        <div class="progress-bar">
            <div id="progress-fill" class="progress-fill" style="width: 0%"></div>
        </div>

        <!-- Step Indicators -->
        <div class="flex items-center justify-center mb-8">
            <div class="step-indicator active" id="step-indicator-1">1</div>
            <div class="step-line" id="step-line-1"></div>
            <div class="step-indicator inactive" id="step-indicator-2">2</div>
            <div class="step-line" id="step-line-2"></div>
            <div class="step-indicator inactive" id="step-indicator-3">3</div>
            <div class="step-line" id="step-line-3"></div>
            <div class="step-indicator inactive" id="step-indicator-4">4</div>
            <div class="step-line" id="step-line-4"></div>
            <div class="step-indicator inactive" id="step-indicator-5">5</div>
        </div>

        <!-- Main Content Area -->
        <div id="main-content" class="max-w-6xl mx-auto">
            <!-- Welcome Step -->
            <div id="step-1" class="step-content">
                <div class="glass-effect rounded-3xl p-8 text-center">
                    <div class="floating-animation mb-6">
                        <i class="fas fa-handshake text-6xl text-yellow-300"></i>
                    </div>
                    <h2 class="text-3xl font-bold text-white mb-6 arabic-text">
                        مرحباً بكم في ورشة إنشاء القصص! 🎭
                    </h2>
                    <div class="collaboration-message">
                        <p class="arabic-text text-lg">
                            أنا مساعدكم الذكي في هذه الرحلة الإبداعية! 🤖✨
                            <br>
                            سنتعاون معاً لإنشاء قصة فريدة ومخصصة لطفلكم الحبيب.
                            <br>
                            أنتم تقدمون الأفكار والتفاصيل، وأنا أحولها إلى حكاية جميلة بلغة عربية فصحى سليمة! 📚
                        </p>
                    </div>
                    <button id="start-collaboration" class="interactive-button text-xl px-8 py-4 arabic-text">
                        <i class="fas fa-rocket ml-3"></i>
                        لنبدأ التعاون! 🚀
                    </button>
                </div>
            </div>

            <!-- Collaboration Workspace -->
            <div id="step-2" class="step-content hidden">
                <div class="collaboration-workspace">
                    <!-- User Side -->
                    <div class="user-side">
                        <h3 class="text-xl font-bold text-white mb-4 arabic-text">
                            <i class="fas fa-user text-green-400 ml-2"></i>
                            دوركم كصانع القصة
                        </h3>
                        <div id="user-inputs">
                            <!-- Dynamic user input areas will be added here -->
                        </div>
                    </div>

                    <!-- AI Side -->
                    <div class="ai-side">
                        <h3 class="text-xl font-bold text-white mb-4 arabic-text">
                            <i class="fas fa-robot text-blue-400 ml-2"></i>
                            دوري كمساعد ذكي
                        </h3>
                        <div id="ai-suggestions">
                            <!-- Dynamic AI suggestions will be added here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="flex justify-center gap-4 mt-8">
            <button id="prev-btn" class="interactive-button arabic-text hidden">
                <i class="fas fa-arrow-right ml-2"></i>
                السابق
            </button>
            <button id="next-btn" class="interactive-button arabic-text hidden">
                <i class="fas fa-arrow-left ml-2"></i>
                التالي
            </button>
        </div>

        <!-- Author Information -->
        <div class="text-center mt-16">
            <div class="glass-effect rounded-2xl p-6 max-w-2xl mx-auto">
                <h3 class="text-xl font-bold text-white mb-3 arabic-text">
                    <i class="fas fa-user-graduate ml-2 text-yellow-300"></i>
                    معلومات المؤلف
                </h3>
                <div class="text-purple-200 arabic-text">
                    <p class="mb-2"><strong>د. محمد يعقوب إسماعيل</strong></p>
                    <p class="mb-2">جامعة السودان للعلوم والتكنولوجيا - الهندسة الطبية الحيوية</p>
                    <p class="mb-2">© 2025 - جميع الحقوق محفوظة</p>
                    <div class="flex justify-center items-center gap-4 mt-4">
                        <a href="mailto:<EMAIL>" class="text-yellow-300 hover:text-yellow-400 transition-colors">
                            <i class="fas fa-envelope ml-1"></i>
                            <EMAIL>
                        </a>
                    </div>
                    <div class="flex justify-center items-center gap-4 mt-2">
                        <span class="text-yellow-300">
                            <i class="fas fa-phone ml-1"></i>
                            +249912867327
                        </span>
                        <span class="text-yellow-300">
                            <i class="fas fa-phone ml-1"></i>
                            +966538076790
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Magic Sparkles -->
    <div class="magic-sparkle" style="top: 10%; left: 10%;">✨</div>
    <div class="magic-sparkle" style="top: 20%; right: 15%;">🌟</div>
    <div class="magic-sparkle" style="bottom: 30%; left: 20%;">✨</div>
    <div class="magic-sparkle" style="bottom: 10%; right: 10%;">🌟</div>

    <!-- JavaScript -->
    <script src="JS/story-resources.js"></script>
    <script src="JS/character-environment-library.js"></script>
    <script src="JS/educational-lessons-system.js"></script>
    <script src="JS/story-endings-system.js"></script>
    <script src="JS/advanced-dialogue-system.js"></script>
    <script src="JS/dynamic-characters-system.js"></script>
    <script src="JS/interactive-story-maker.js"></script>
</body>
</html>
