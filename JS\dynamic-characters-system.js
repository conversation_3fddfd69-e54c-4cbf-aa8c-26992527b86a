// 🎪 نظام الشخصيات الديناميكية المتطور 👥
// Dynamic Characters System with Rich Personalities
// Dr.<PERSON>, SUST -BME, @ 2025

class DynamicCharactersSystem {
    constructor() {
        this.initializeCharacters();
    }

    initializeCharacters() {
        this.characterArchetypes = this.getCharacterArchetypes();
        this.familyDynamics = this.getFamilyDynamics();
        this.characterGrowth = this.getCharacterGrowth();
        this.relationshipPatterns = this.getRelationshipPatterns();
        this.culturalBackgrounds = this.getCulturalBackgrounds();
    }

    // ===== أنماط الشخصيات الأساسية =====
    
    getCharacterArchetypes() {
        return {
            the_explorer: {
                name_variants: {
                    boys: ['كريم المستكشف', 'عمر الجريء', 'يوسف المغامر'],
                    girls: ['نور المكتشفة', 'سلمى الشجاعة', 'ليلى المغامرة']
                },
                core_traits: {
                    primary: 'حب الاستطلاع والمغامرة',
                    secondary: ['شجاع', 'فضولي', 'نشيط', 'متفائل'],
                    growth_arc: 'من الفضول إلى الحكمة'
                },
                dialogue_signature: {
                    catchphrases: [
                        'دعونا نكتشف ما هناك!',
                        'أشعر أن مغامرة رائعة تنتظرنا!',
                        'لن نعرف إلا إذا جربنا!'
                    ],
                    question_style: 'أسئلة مفتوحة ومثيرة',
                    reaction_patterns: [
                        'عند الاكتشاف: "واااو! هذا مذهل!"',
                        'عند التحدي: "هذا يبدو ممتعاً!"',
                        'عند المساعدة: "سأجد طريقة!"'
                    ]
                },
                voice_profile: {
                    energy_level: 'عالي ومتحمس',
                    speech_pace: 'سريع عند الإثارة، متأني عند التفكير',
                    volume: 'مرتفع عند الاكتشاف',
                    tone_variations: [
                        'متحمس عند رؤية شيء جديد',
                        'مركز عند حل المشاكل',
                        'مشجع للآخرين'
                    ]
                }
            },

            the_caretaker: {
                name_variants: {
                    boys: ['أحمد الحنون', 'محمد الطيب', 'عبدالله الرحيم'],
                    girls: ['فاطمة الحنونة', 'عائشة الطيبة', 'خديجة الرحيمة']
                },
                core_traits: {
                    primary: 'الرعاية والاهتمام بالآخرين',
                    secondary: ['عطوف', 'مسؤول', 'صبور', 'حكيم'],
                    growth_arc: 'من الرعاية إلى القيادة الحكيمة'
                },
                dialogue_signature: {
                    catchphrases: [
                        'لا تقلق، سأعتني بك',
                        'كلنا نحتاج للمساعدة أحياناً',
                        'الأهم أن نكون معاً'
                    ],
                    question_style: 'أسئلة تعاطفية ومهتمة',
                    reaction_patterns: [
                        'عند رؤية محتاج: "المسكين، كيف يمكنني مساعدته؟"',
                        'عند النجاح: "الحمد لله، تمكنا من مساعدته"',
                        'عند التشجيع: "أنت قادر على ذلك، أثق بك"'
                    ]
                },
                voice_profile: {
                    energy_level: 'هادئ ومطمئن',
                    speech_pace: 'متوسط ومتأني',
                    volume: 'ناعم ودافئ',
                    tone_variations: [
                        'حنون عند التعامل مع المحتاجين',
                        'حازم عند الحاجة للحماية',
                        'مشجع عند دعم الآخرين'
                    ]
                }
            },

            the_thinker: {
                name_variants: {
                    boys: ['حسام المفكر', 'طارق الذكي', 'زياد الحكيم'],
                    girls: ['زينب الذكية', 'مريم المفكرة', 'هدى الحكيمة']
                },
                core_traits: {
                    primary: 'التفكير العميق وحل المشاكل',
                    secondary: ['ذكي', 'متأني', 'منطقي', 'مبدع'],
                    growth_arc: 'من التفكير إلى الحكمة العملية'
                },
                dialogue_signature: {
                    catchphrases: [
                        'دعني أفكر في هذا قليلاً...',
                        'هناك طريقة أفضل لفعل هذا',
                        'إذا نظرنا للأمر من زاوية أخرى...'
                    ],
                    question_style: 'أسئلة تحليلية ومنطقية',
                    reaction_patterns: [
                        'عند المشكلة: "هممم، ما هي الخيارات المتاحة؟"',
                        'عند الحل: "أعتقد أن لدي فكرة!"',
                        'عند النجاح: "كما توقعت، الخطة نجحت"'
                    ]
                },
                voice_profile: {
                    energy_level: 'متوسط ومتحكم',
                    speech_pace: 'بطيء ومدروس',
                    volume: 'متوسط وواضح',
                    tone_variations: [
                        'متأمل عند التفكير',
                        'واثق عند تقديم الحلول',
                        'صبور عند الشرح'
                    ]
                }
            },

            the_entertainer: {
                name_variants: {
                    boys: ['سامي المرح', 'عادل المضحك', 'ياسر البهيج'],
                    girls: ['سارة المرحة', 'دانا المضحكة', 'لينا البهيجة']
                },
                core_traits: {
                    primary: 'إدخال السرور والبهجة',
                    secondary: ['مرح', 'اجتماعي', 'متفائل', 'مبدع'],
                    growth_arc: 'من المرح إلى القيادة الإيجابية'
                },
                dialogue_signature: {
                    catchphrases: [
                        'هيا نجعل هذا ممتعاً!',
                        'الحياة أجمل عندما نضحك',
                        'لدي فكرة رائعة!'
                    ],
                    question_style: 'أسئلة مرحة ومبدعة',
                    reaction_patterns: [
                        'عند الملل: "دعونا نلعب لعبة!"',
                        'عند الحزن: "سأجعلك تبتسم!"',
                        'عند النجاح: "يييي! هذا رائع!"'
                    ]
                },
                voice_profile: {
                    energy_level: 'عالي ومفعم بالحيوية',
                    speech_pace: 'متغير حسب الموقف',
                    volume: 'مرتفع ومعبر',
                    tone_variations: [
                        'مرح عند اللعب',
                        'مشجع عند رفع المعنويات',
                        'إبداعي عند اقتراح الأفكار'
                    ]
                }
            }
        };
    }

    // ===== ديناميكيات العائلة =====
    
    getFamilyDynamics() {
        return {
            parent_child_relationships: {
                wise_father: {
                    personality: 'حكيم وصبور ومرشد',
                    dialogue_style: {
                        teaching_moments: [
                            'هذا درس مهم يا بني/بنتي',
                            'ما رأيك لو فكرنا في هذا معاً؟',
                            'أنا فخور بك لأنك...'
                        ],
                        encouragement: [
                            'أثق في قدرتك على اتخاذ القرار الصحيح',
                            'كل خطأ فرصة للتعلم',
                            'الشجاعة ليست عدم الخوف، بل...'
                        ],
                        guidance: [
                            'دعني أشاركك خبرتي',
                            'عندما كنت في عمرك...',
                            'الحياة علمتني أن...'
                        ]
                    },
                    voice_characteristics: {
                        tone: 'عميق ودافئ',
                        pace: 'متأني وحكيم',
                        volume: 'متوسط ومطمئن'
                    }
                },

                loving_mother: {
                    personality: 'حنونة ومحبة وحامية',
                    dialogue_style: {
                        nurturing: [
                            'تعال هنا يا حبيب قلبي',
                            'ماما هنا دائماً من أجلك',
                            'كل شيء سيكون بخير'
                        ],
                        worry_care: [
                            'هل أنت بخير؟ هل تحتاج شيئاً؟',
                            'احذر يا عزيزي',
                            'دعني أتأكد أنك آمن'
                        ],
                        pride: [
                            'ما أجملك وأطيبك!',
                            'قلبي يفرح عندما أراك سعيداً',
                            'أنت نور عيني'
                        ]
                    },
                    voice_characteristics: {
                        tone: 'ناعم وحنون',
                        pace: 'متوسط ومريح',
                        volume: 'دافئ ومحب'
                    }
                }
            },

            sibling_dynamics: {
                older_sibling: {
                    role: 'المرشد والحامي',
                    dialogue_patterns: [
                        'تعال معي، سأريك كيف',
                        'لا تقلق، أنا هنا لحمايتك',
                        'دعني أساعدك في هذا'
                    ],
                    relationship_style: 'مسؤول ومشجع'
                },

                younger_sibling: {
                    role: 'المتعلم والمقلد',
                    dialogue_patterns: [
                        'أريد أن أفعل مثلك!',
                        'علمني كيف أفعل ذلك',
                        'هل يمكنني المساعدة؟'
                    ],
                    relationship_style: 'متحمس ومعجب'
                },

                twin_siblings: {
                    role: 'الشراكة والتكامل',
                    dialogue_patterns: [
                        'أعرف ما تفكر فيه!',
                        'هيا نفعل هذا معاً',
                        'نحن فريق واحد'
                    ],
                    relationship_style: 'متناغم ومتعاون'
                }
            }
        };
    }

    // ===== نمو الشخصية =====
    
    getCharacterGrowth() {
        return {
            story_arc_stages: {
                introduction: {
                    purpose: 'تقديم الشخصية وسماتها',
                    dialogue_focus: 'إظهار الشخصية الأساسية',
                    example_moments: [
                        'الطفل الفضولي يلاحظ شيئاً غريباً',
                        'الطفل الحنون يهتم بالآخرين',
                        'الطفل المفكر يحلل الموقف'
                    ]
                },

                challenge: {
                    purpose: 'اختبار الشخصية وتطويرها',
                    dialogue_focus: 'إظهار الصراع الداخلي والنمو',
                    example_moments: [
                        'الطفل الخجول يجد الشجاعة',
                        'الطفل المتهور يتعلم الصبر',
                        'الطفل الأناني يكتشف قيمة المشاركة'
                    ]
                },

                resolution: {
                    purpose: 'إظهار النمو والتعلم',
                    dialogue_focus: 'التعبير عن الدرس المستفاد',
                    example_moments: [
                        'الطفل يطبق ما تعلمه',
                        'الطفل يساعد آخرين بخبرته الجديدة',
                        'الطفل يعبر عن فهمه للدرس'
                    ]
                }
            },

            emotional_development: {
                fear_to_courage: {
                    stages: ['خوف', 'تردد', 'محاولة', 'نجاح', 'ثقة'],
                    dialogue_progression: [
                        'أشعر بالخوف...',
                        'ربما يمكنني المحاولة...',
                        'سأجرب بحذر...',
                        'نجحت! لم يكن صعباً!',
                        'أنا قادر على فعل أشياء رائعة!'
                    ]
                },

                selfishness_to_sharing: {
                    stages: ['أنانية', 'إدراك', 'تجربة', 'اكتشاف', 'عطاء'],
                    dialogue_progression: [
                        'هذا ملكي وحدي!',
                        'ربما الآخرون يريدون أيضاً...',
                        'حسناً، يمكنك اللعب قليلاً...',
                        'اللعب معاً أكثر متعة!',
                        'دعونا نشارك كل شيء!'
                    ]
                }
            }
        };
    }

    // ===== أنماط العلاقات =====
    
    getRelationshipPatterns() {
        return {
            friendship_building: {
                stages: ['لقاء', 'اهتمام', 'تفاعل', 'ثقة', 'صداقة'],
                dialogue_examples: [
                    'مرحباً، ما اسمك؟',
                    'تبدو لطيفاً، هل تريد اللعب؟',
                    'أحب نفس الأشياء التي تحبها!',
                    'يمكنني أن أثق بك',
                    'أنت أفضل صديق لي!'
                ]
            },

            conflict_resolution: {
                stages: ['خلاف', 'غضب', 'تفكير', 'تفهم', 'مصالحة'],
                dialogue_examples: [
                    'لست موافقاً على هذا!',
                    'أنا غاضب منك!',
                    'ربما كان لديك سبب...',
                    'أفهم وجهة نظرك الآن',
                    'آسف، دعونا نكون أصدقاء مرة أخرى'
                ]
            },

            mentorship: {
                mentor_qualities: ['صبور', 'حكيم', 'مشجع', 'متفهم'],
                mentee_qualities: ['متحمس', 'متعلم', 'مقدر', 'متطور'],
                interaction_patterns: [
                    'المعلم يشارك الخبرة',
                    'الطالب يسأل ويتعلم',
                    'المعلم يشجع المحاولة',
                    'الطالب يطبق ما تعلم',
                    'كلاهما يحتفل بالنجاح'
                ]
            }
        };
    }

    // ===== الخلفيات الثقافية =====
    
    getCulturalBackgrounds() {
        return {
            traditional_values: {
                respect_for_elders: {
                    expressions: [
                        'نعم يا عمي/خالي',
                        'كما تشاء يا جدي/جدتي',
                        'أستأذنك يا أبي/أمي'
                    ],
                    behaviors: ['الوقوف عند دخول الكبار', 'تقبيل اليد', 'الاستماع بأدب']
                },

                hospitality: {
                    expressions: [
                        'أهلاً وسهلاً',
                        'بيتك بيتنا',
                        'تفضل، اشرب شاي'
                    ],
                    behaviors: ['تقديم الطعام', 'الترحيب الحار', 'الاهتمام بالضيف']
                },

                community_spirit: {
                    expressions: [
                        'الجار قبل الدار',
                        'يد واحدة لا تصفق',
                        'في الاتحاد قوة'
                    ],
                    behaviors: ['مساعدة الجيران', 'العمل الجماعي', 'التكافل الاجتماعي']
                }
            },

            modern_adaptations: {
                technology_integration: 'استخدام التكنولوجيا بحكمة',
                global_awareness: 'فهم العالم مع الحفاظ على الهوية',
                environmental_consciousness: 'الاهتمام بالبيئة والطبيعة'
            }
        };
    }

    // ===== دوال إنشاء الشخصيات =====
    
    createDynamicCharacter(archetype, age, gender, cultural_background = 'traditional') {
        const base_archetype = this.characterArchetypes[archetype];
        const cultural_elements = this.culturalBackgrounds[cultural_background];
        
        return {
            name: this.selectName(base_archetype, gender),
            personality: this.buildPersonality(base_archetype, cultural_elements),
            dialogue_style: this.createDialogueStyle(base_archetype, age),
            voice_profile: this.adaptVoiceProfile(base_archetype.voice_profile, age),
            growth_potential: this.defineGrowthArc(base_archetype, age),
            relationships: this.initializeRelationships(),
            cultural_traits: cultural_elements
        };
    }

    selectName(archetype, gender) {
        const names = archetype.name_variants[gender === 'girl' ? 'girls' : 'boys'];
        return names[Math.floor(Math.random() * names.length)];
    }

    buildPersonality(archetype, cultural_elements) {
        return {
            core_trait: archetype.core_traits.primary,
            secondary_traits: archetype.core_traits.secondary,
            cultural_values: Object.keys(cultural_elements.traditional_values),
            growth_direction: archetype.core_traits.growth_arc
        };
    }

    createDialogueStyle(archetype, age) {
        const base_style = archetype.dialogue_signature;
        const age_adaptations = this.getAgeAdaptations(age);
        
        return {
            catchphrases: base_style.catchphrases,
            question_style: base_style.question_style,
            reactions: base_style.reaction_patterns,
            vocabulary_level: age_adaptations.vocabulary,
            sentence_complexity: age_adaptations.complexity
        };
    }

    getAgeAdaptations(age) {
        if (age <= 5) {
            return { vocabulary: 'simple', complexity: 'basic' };
        } else if (age <= 8) {
            return { vocabulary: 'intermediate', complexity: 'moderate' };
        } else {
            return { vocabulary: 'advanced', complexity: 'complex' };
        }
    }

    adaptVoiceProfile(base_profile, age) {
        return {
            ...base_profile,
            age_adjustments: {
                pitch: age <= 6 ? 'higher' : 'normal',
                articulation: age <= 5 ? 'simple' : 'clear',
                emotional_range: age >= 8 ? 'full' : 'basic'
            }
        };
    }

    defineGrowthArc(archetype, age) {
        const base_arc = archetype.core_traits.growth_arc;
        const age_appropriate_challenges = this.getAgeChallenges(age);
        
        return {
            starting_point: base_arc.split(' إلى ')[0],
            end_goal: base_arc.split(' إلى ')[1],
            challenges: age_appropriate_challenges,
            milestones: this.createMilestones(age)
        };
    }

    getAgeChallenges(age) {
        const challenges = {
            young: ['التعبير عن المشاعر', 'المشاركة', 'الطاعة'],
            middle: ['حل المشاكل', 'الصداقة', 'المسؤولية'],
            older: ['القيادة', 'اتخاذ القرارات', 'التعاطف العميق']
        };
        
        if (age <= 5) return challenges.young;
        if (age <= 8) return challenges.middle;
        return challenges.older;
    }

    createMilestones(age) {
        return [
            'التعرف على الذات',
            'بناء العلاقات',
            'مواجهة التحديات',
            'تعلم الدروس',
            'تطبيق المعرفة'
        ].slice(0, Math.min(age - 2, 5));
    }

    initializeRelationships() {
        return {
            family: { closeness: 'high', dynamics: 'supportive' },
            friends: { count: 0, quality: 'developing' },
            mentors: { present: false, type: null },
            pets: { has_pet: false, relationship: null }
        };
    }
}

// تصدير الفئة للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DynamicCharactersSystem;
}
