class CollaborativeWriting {
    constructor() {
        this.storyData = this.loadStoryData();
        this.currentSection = 0;
        this.sections = [
            {
                title: 'البداية - تقديم الشخصيات',
                description: 'أخبرنا عن الشخصية الرئيسية وأين تحدث القصة',
                suggestions: [
                    'طفل/طفلة يحب المغامرات',
                    'حيوان أليف مميز',
                    'مكان سحري أو مثير',
                    'مشكلة أو تحدي يواجه البطل'
                ]
            },
            {
                title: 'التطوير - بناء الأحداث',
                description: 'ماذا يحدث للشخصية؟ ما المشكلة التي تواجهها؟',
                suggestions: [
                    'مغامرة مثيرة',
                    'لقاء شخصية جديدة',
                    'اكتشاف شيء مهم',
                    'تحدي يحتاج حل'
                ]
            },
            {
                title: 'الذروة - اللحظة المهمة',
                description: 'اللحظة الأكثر إثارة في القصة',
                suggestions: [
                    'حل المشكلة بطريقة ذكية',
                    'مساعدة الآخرين',
                    'اكتشاف الحقيقة',
                    'التغلب على الخوف'
                ]
            },
            {
                title: 'النهاية - الخاتمة السعيدة',
                description: 'كيف تنتهي القصة؟ ما الدرس المستفاد؟',
                suggestions: [
                    'نهاية سعيدة للجميع',
                    'درس أخلاقي مهم',
                    'صداقة جديدة',
                    'نمو الشخصية'
                ]
            }
        ];
        this.storyContent = [];
        this.wordCount = 0;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateCurrentSection();
        this.createFloatingParticles();
        this.updateProgress();

        // عرض البيانات المحفوظة
        this.displayUserData();

        // تحميل القصة المحفوظة إن وجدت
        if (this.storyData && this.storyData.savedContent) {
            this.loadSavedStory();
        }

        // تخصيص الاقتراحات حسب البيانات
        this.customizeSuggestions();
    }

    loadStoryData() {
        const data = localStorage.getItem('collaborativeStoryData');
        return data ? JSON.parse(data) : null;
    }

    bindEvents() {
        // إضافة للقصة
        document.getElementById('add-to-story').addEventListener('click', () => {
            this.addToStory();
        });

        // الحصول على اقتراحات
        document.getElementById('get-suggestions').addEventListener('click', () => {
            this.showSuggestions();
        });

        // القسم التالي
        document.getElementById('next-section').addEventListener('click', () => {
            this.nextSection();
        });

        // العودة للصانع
        document.getElementById('back-to-maker').addEventListener('click', () => {
            this.goBackToMaker();
        });

        // العودة للصفحة الرئيسية
        document.getElementById('back-to-home').addEventListener('click', () => {
            this.goToHomePage();
        });

        // حفظ القصة
        document.getElementById('save-story').addEventListener('click', () => {
            this.saveStory();
        });

        // تصدير القصة
        document.getElementById('export-story').addEventListener('click', () => {
            this.exportStory();
        });

        // مشاركة القصة
        document.getElementById('share-story').addEventListener('click', () => {
            this.shareStory();
        });

        // تحديث عدد الكلمات
        document.getElementById('user-input').addEventListener('input', (e) => {
            this.updateWordCount(e.target.value);
        });

        // Enter للإضافة السريعة
        document.getElementById('user-input').addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.addToStory();
            }
        });
    }

    updateCurrentSection() {
        const section = this.sections[this.currentSection];
        document.getElementById('current-section').textContent = section.title;
        document.getElementById('section-description').textContent = section.description;
        
        // إخفاء الاقتراحات
        document.getElementById('suggestions-container').classList.add('hidden');
    }

    showSuggestions() {
        const section = this.sections[this.currentSection];
        const container = document.getElementById('suggestions-container');
        const list = document.getElementById('suggestions-list');
        
        list.innerHTML = '';
        
        section.suggestions.forEach(suggestion => {
            const card = document.createElement('div');
            card.className = 'suggestion-card';
            card.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${suggestion}</span>
                    <i class="fas fa-plus text-blue-500"></i>
                </div>
            `;
            
            card.addEventListener('click', () => {
                const currentInput = document.getElementById('user-input').value;
                const newInput = currentInput ? `${currentInput}\n${suggestion}` : suggestion;
                document.getElementById('user-input').value = newInput;
                this.updateWordCount(newInput);
                container.classList.add('hidden');
            });
            
            list.appendChild(card);
        });
        
        container.classList.remove('hidden');
    }

    addToStory() {
        const userInput = document.getElementById('user-input').value.trim();
        
        if (!userInput) {
            this.showNotification('يرجى كتابة شيء أولاً!', 'warning');
            return;
        }

        // إظهار مؤشر الكتابة
        this.showTypingIndicator();

        // محاكاة معالجة الذكاء الاصطناعي
        setTimeout(() => {
            const enhancedContent = this.enhanceUserInput(userInput);
            this.appendToStory(enhancedContent);
            
            // مسح المدخل
            document.getElementById('user-input').value = '';
            this.updateWordCount('');
            
            // إخفاء مؤشر الكتابة
            this.hideTypingIndicator();
            
            // تفعيل أزرار الحفظ
            this.enableStoryActions();
            
            // تحديث التقدم
            this.updateProgress();
            
            this.showNotification('تمت إضافة مساهمتك بنجاح!', 'success');
        }, 2000 + Math.random() * 2000);
    }

    enhanceUserInput(input) {
        const section = this.sections[this.currentSection];

        // تحسين النص بناءً على القسم الحالي والإعدادات
        let enhanced = this.addArabicFlair(input);

        // تطبيق الإعدادات المحفوظة
        enhanced = this.applyUserSettings(enhanced);

        // إضافة عناصر تفاعلية حسب البيانات
        enhanced = this.addInteractiveElements(enhanced);

        // إضافة توجيهات صوتية
        enhanced = this.addVoiceDirections(enhanced);

        return enhanced;
    }

    applyUserSettings(text) {
        if (!this.storyData || !this.storyData.userInputs) return text;

        const userData = this.storyData.userInputs;
        let enhanced = text;

        // استبدال أسماء عامة بالاسم المحفوظ
        const names = this.extractNames(userData);
        if (names.length > 0) {
            enhanced = enhanced.replace(/البطل|الطفل|الشخصية الرئيسية/g, names[0]);
        }

        // تطبيق النبرة المختارة
        if (userData.preferences && userData.preferences.storyTone) {
            enhanced = this.applyTone(enhanced, userData.preferences.storyTone);
        }

        // تطبيق طول القصة
        if (userData.preferences && userData.preferences.storyLength) {
            enhanced = this.adjustLength(enhanced, userData.preferences.storyLength);
        }

        return enhanced;
    }

    extractNames(userData) {
        const names = [];
        for (let key in userData) {
            if (key.includes('name') || key.includes('اسم')) {
                if (userData[key] && typeof userData[key] === 'string') {
                    names.push(userData[key]);
                }
            }
        }
        return names;
    }

    applyTone(text, tone) {
        const toneEnhancements = {
            'gentle': {
                prefix: 'بهدوء ولطف، ',
                suffix: '، في جو من السكينة والطمأنينة',
                replacements: {
                    'قال': 'همس بلطف',
                    'صرخ': 'قال بصوت ناعم',
                    'جرى': 'مشى بهدوء'
                }
            },
            'exciting': {
                prefix: 'وفجأة، ',
                suffix: '، في مغامرة مثيرة ومشوقة!',
                replacements: {
                    'قال': 'صرخ بحماس',
                    'مشى': 'جرى بسرعة',
                    'نظر': 'حدق بإثارة'
                }
            },
            'educational': {
                prefix: 'وهنا تعلم، ',
                suffix: '، واكتشف شيئاً جديداً ومفيداً',
                replacements: {
                    'فعل': 'تعلم كيف يفعل',
                    'رأى': 'لاحظ وتعلم من',
                    'وجد': 'اكتشف'
                }
            },
            'funny': {
                prefix: 'وبطريقة مضحكة، ',
                suffix: '، مما جعل الجميع يضحكون بسعادة!',
                replacements: {
                    'قال': 'قال بطريقة مضحكة',
                    'فعل': 'فعل بطريقة طريفة',
                    'نظر': 'نظر نظرة مضحكة'
                }
            }
        };

        const enhancement = toneEnhancements[tone];
        if (!enhancement) return text;

        let enhanced = text;

        // تطبيق الاستبدالات
        Object.keys(enhancement.replacements).forEach(original => {
            const replacement = enhancement.replacements[original];
            enhanced = enhanced.replace(new RegExp(original, 'g'), replacement);
        });

        // إضافة البادئة والخاتمة أحياناً
        if (Math.random() > 0.7) {
            enhanced = enhancement.prefix + enhanced;
        }
        if (Math.random() > 0.8) {
            enhanced = enhanced + enhancement.suffix;
        }

        return enhanced;
    }

    adjustLength(text, length) {
        // تعديل طول النص حسب التفضيل
        if (length === 'short') {
            // تقصير النص
            return text.split('.')[0] + '.';
        } else if (length === 'long') {
            // إضافة تفاصيل
            return text + ' وكان هذا بداية لمغامرة رائعة مليئة بالتفاصيل الجميلة.';
        }
        return text; // متوسط
    }

    addInteractiveElements(text) {
        if (!this.storyData || !this.storyData.storyElements) return text;

        const elements = this.storyData.storyElements;
        let enhanced = text;

        elements.forEach(element => {
            if (element.type === 'sounds') {
                enhanced = enhanced.replace(/قال/g, 'قال "كلام جميل"');
            } else if (element.type === 'actions') {
                enhanced += '\n\n🤸‍♀️ يمكنك تقليد هذه الحركة!';
            } else if (element.type === 'questions') {
                enhanced += '\n\n❓ ما رأيك في هذا الموقف؟';
            }
        });

        return enhanced;
    }

    addArabicFlair(text) {
        // إضافة تعبيرات عربية أصيلة
        const openings = ['وفي تلك اللحظة', 'وبينما كان', 'وفجأة', 'وعندما'];
        const transitions = ['ثم', 'بعد ذلك', 'وهكذا', 'وفي النهاية'];
        
        let enhanced = text;
        
        // إضافة افتتاحية مناسبة للقسم
        if (this.currentSection === 0 && !enhanced.startsWith('كان يا ما كان')) {
            enhanced = `كان يا ما كان، ${enhanced}`;
        }
        
        // تحسين التدفق
        if (this.storyContent.length > 0) {
            const randomOpening = openings[Math.floor(Math.random() * openings.length)];
            enhanced = `${randomOpening}، ${enhanced}`;
        }
        
        return enhanced;
    }

    addVoiceDirections(text) {
        // إضافة توجيهات صوتية بسيطة
        const emotions = {
            'فرح': '*(بصوت مرح ومتحمس)*',
            'حزن': '*(بصوت حزين ومتأثر)*',
            'خوف': '*(بصوت خائف ومتردد)*',
            'إثارة': '*(بصوت متحمس ومثير)*'
        };
        
        // إضافة توجيهات عشوائية
        if (Math.random() > 0.7) {
            const emotionKeys = Object.keys(emotions);
            const randomEmotion = emotionKeys[Math.floor(Math.random() * emotionKeys.length)];
            text += `\n\n${emotions[randomEmotion]}`;
        }
        
        return text;
    }

    appendToStory(content) {
        this.storyContent.push({
            section: this.currentSection,
            content: content,
            timestamp: new Date().toISOString()
        });
        
        const output = document.getElementById('story-output');
        
        // إنشاء عنصر جديد للمحتوى
        const storyPart = document.createElement('div');
        storyPart.className = 'story-part mb-4 p-3 bg-white rounded-lg border-r-4 border-blue-500';
        storyPart.innerHTML = `
            <div class="text-sm text-gray-500 mb-2">
                <i class="fas fa-feather-alt ml-1"></i>
                ${this.sections[this.currentSection].title}
            </div>
            <div class="story-text">${content}</div>
        `;
        
        // إضافة تأثير ظهور
        storyPart.style.opacity = '0';
        storyPart.style.transform = 'translateY(20px)';
        
        // مسح الرسالة الافتراضية إذا كانت موجودة
        if (this.storyContent.length === 1) {
            output.innerHTML = '';
        }
        
        output.appendChild(storyPart);
        
        // تأثير الظهور
        setTimeout(() => {
            storyPart.style.transition = 'all 0.5s ease';
            storyPart.style.opacity = '1';
            storyPart.style.transform = 'translateY(0)';
        }, 100);
        
        // التمرير للأسفل
        output.scrollTop = output.scrollHeight;
    }

    showTypingIndicator() {
        document.getElementById('typing-indicator').style.display = 'flex';
        document.getElementById('add-to-story').disabled = true;
    }

    hideTypingIndicator() {
        document.getElementById('typing-indicator').style.display = 'none';
        document.getElementById('add-to-story').disabled = false;
    }

    enableStoryActions() {
        document.getElementById('save-story').disabled = false;
        document.getElementById('export-story').disabled = false;
        document.getElementById('share-story').disabled = false;
        
        // تفعيل الانتقال للقسم التالي إذا كان هناك محتوى كافي
        if (this.storyContent.filter(item => item.section === this.currentSection).length >= 1) {
            document.getElementById('next-section').disabled = false;
        }
    }

    nextSection() {
        if (this.currentSection < this.sections.length - 1) {
            this.currentSection++;
            this.updateCurrentSection();
            this.updateProgress();
            document.getElementById('next-section').disabled = true;
            
            this.showNotification(`انتقلت إلى: ${this.sections[this.currentSection].title}`, 'info');
        } else {
            this.showNotification('تهانينا! لقد أكملت القصة!', 'success');
            this.completeStory();
        }
    }

    updateProgress() {
        const progress = ((this.currentSection + 1) / this.sections.length) * 100;
        document.getElementById('progress-fill').style.width = `${progress}%`;
        document.getElementById('progress-percentage').textContent = `${Math.round(progress)}%`;
    }

    updateWordCount(text) {
        const words = text.trim() ? text.trim().split(/\s+/).length : 0;
        document.getElementById('word-count').textContent = `${words} كلمة`;
    }

    createFloatingParticles() {
        const particles = document.getElementById('particles');
        const emojis = ['✨', '📚', '🎭', '🌟', '💫', '🎨', '📖', '✍️'];
        
        for (let i = 0; i < 12; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.textContent = emojis[Math.floor(Math.random() * emojis.length)];
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            particle.style.animationDuration = (4 + Math.random() * 4) + 's';
            particles.appendChild(particle);
        }
    }

    goBackToMaker() {
        if (this.storyContent.length > 0) {
            if (confirm('هل أنت متأكد؟ سيتم حفظ تقدمك تلقائياً.')) {
                this.saveStory();
                window.location.href = 'interactive-story-maker.html';
            }
        } else {
            window.location.href = 'interactive-story-maker.html';
        }
    }

    goToHomePage() {
        if (this.storyContent.length > 0) {
            if (confirm('هل أنت متأكد؟ سيتم حفظ تقدمك تلقائياً.')) {
                this.saveStory();
                window.location.href = 'index.html';
            }
        } else {
            window.location.href = 'index.html';
        }
    }

    saveStory() {
        const storyData = {
            ...this.storyData,
            savedContent: this.storyContent,
            currentSection: this.currentSection,
            lastSaved: new Date().toISOString()
        };
        
        localStorage.setItem('collaborativeStoryData', JSON.stringify(storyData));
        this.showNotification('تم حفظ القصة بنجاح!', 'success');
    }

    exportStory() {
        const fullStory = this.storyContent.map(item => item.content).join('\n\n');
        const blob = new Blob([fullStory], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'قصتي_التعاونية.txt';
        a.click();
        
        URL.revokeObjectURL(url);
        this.showNotification('تم تصدير القصة!', 'success');
    }

    shareStory() {
        const fullStory = this.storyContent.map(item => item.content).join('\n\n');
        
        if (navigator.share) {
            navigator.share({
                title: 'قصتي التعاونية',
                text: fullStory
            });
        } else {
            // نسخ للحافظة
            navigator.clipboard.writeText(fullStory).then(() => {
                this.showNotification('تم نسخ القصة للحافظة!', 'success');
            });
        }
    }

    loadSavedStory() {
        this.storyContent = this.storyData.savedContent;
        this.currentSection = this.storyData.currentSection || 0;
        
        const output = document.getElementById('story-output');
        output.innerHTML = '';
        
        this.storyContent.forEach(item => {
            this.appendToStory(item.content);
        });
        
        this.updateCurrentSection();
        this.updateProgress();
        this.enableStoryActions();
    }

    completeStory() {
        // إضافة خاتمة جميلة
        const conclusion = "وهكذا انتهت قصتنا الجميلة... 🌟\n\nشكراً لك على الكتابة معنا! 📚✨";
        this.appendToStory(conclusion);
        
        // إخفاء أزرار التنقل
        document.getElementById('next-section').style.display = 'none';
        
        this.showNotification('تهانينا! لقد أكملت قصة رائعة!', 'success');
    }

    // ===== دوال التخصيص والاستجابة للإعدادات =====

    displayUserData() {
        if (!this.storyData || !this.storyData.userInputs) return;

        // إنشاء قسم عرض البيانات
        const dataDisplay = document.createElement('div');
        dataDisplay.className = 'user-data-display bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4';
        dataDisplay.innerHTML = `
            <h4 class="font-semibold text-blue-800 mb-2 flex items-center">
                <i class="fas fa-user-circle ml-2"></i>
                بياناتك المحفوظة
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                ${this.formatUserData()}
            </div>
        `;

        // إدراج البيانات في بداية لوحة الإدخال
        const inputPanel = document.querySelector('.input-panel');
        inputPanel.insertBefore(dataDisplay, inputPanel.firstChild.nextSibling);
    }

    formatUserData() {
        const data = this.storyData.userInputs;
        let html = '';

        // عرض البيانات المهمة
        Object.keys(data).forEach(key => {
            if (data[key] && typeof data[key] === 'string' && data[key].trim()) {
                const label = this.getFieldLabel(key);
                html += `
                    <div class="data-item">
                        <span class="font-medium text-gray-700">${label}:</span>
                        <span class="text-gray-600">${data[key]}</span>
                    </div>
                `;
            } else if (Array.isArray(data[key]) && data[key].length > 0) {
                const label = this.getFieldLabel(key);
                html += `
                    <div class="data-item">
                        <span class="font-medium text-gray-700">${label}:</span>
                        <span class="text-gray-600">${data[key].join(', ')}</span>
                    </div>
                `;
            }
        });

        return html || '<div class="text-gray-500">لا توجد بيانات محفوظة</div>';
    }

    getFieldLabel(key) {
        const labels = {
            'child-name': 'اسم الطفل',
            'child-age': 'العمر',
            'story-theme': 'موضوع القصة',
            'story-length': 'طول القصة',
            'story-tone': 'نبرة القصة',
            'storyElements': 'عناصر القصة',
            'preferences': 'التفضيلات',
            'currentStep': 'الخطوة الحالية'
        };
        return labels[key] || key;
    }

    customizeSuggestions() {
        if (!this.storyData || !this.storyData.userInputs) return;

        const userData = this.storyData.userInputs;

        // تخصيص الاقتراحات حسب البيانات
        this.sections.forEach((section, index) => {
            section.suggestions = this.generateCustomSuggestions(section, userData, index);
        });
    }

    generateCustomSuggestions(section, userData, sectionIndex) {
        let suggestions = [...section.suggestions]; // نسخ الاقتراحات الأصلية

        // إضافة اقتراحات مخصصة حسب البيانات
        if (sectionIndex === 0) { // البداية
            if (userData.storyElements) {
                userData.storyElements.forEach(element => {
                    if (element.value) {
                        suggestions.push(`قصة تتضمن ${element.value}`);
                    }
                });
            }

            // اقتراحات حسب العمر
            const age = this.extractAge(userData);
            if (age) {
                if (age <= 5) {
                    suggestions.push('طفل صغير يتعلم أشياء جديدة');
                } else if (age <= 8) {
                    suggestions.push('طفل يحب الاستكشاف والمغامرات');
                } else {
                    suggestions.push('طفل ذكي يحل المشاكل بطريقة إبداعية');
                }
            }
        }

        // تخصيص حسب النبرة المختارة
        if (userData.preferences && userData.preferences.storyTone) {
            const tone = userData.preferences.storyTone;
            suggestions = this.adaptSuggestionsToTone(suggestions, tone);
        }

        return suggestions.slice(0, 6); // الحد الأقصى 6 اقتراحات
    }

    extractAge(userData) {
        // البحث عن العمر في البيانات
        for (let key in userData) {
            if (key.includes('age') || key.includes('عمر')) {
                const age = parseInt(userData[key]);
                if (!isNaN(age) && age > 0 && age < 18) {
                    return age;
                }
            }
        }
        return null;
    }

    adaptSuggestionsToTone(suggestions, tone) {
        const toneAdaptations = {
            'gentle': suggestions.map(s => s.replace(/مثير|مغامرة/, 'هادئ ولطيف')),
            'exciting': suggestions.map(s => s + ' مليء بالإثارة والمغامرة'),
            'educational': suggestions.map(s => s + ' مع تعلم شيء جديد'),
            'funny': suggestions.map(s => s + ' بطريقة مضحكة ومرحة')
        };

        return toneAdaptations[tone] || suggestions;
    }

    showNotification(message, type = 'info') {
        // إنشاء إشعار بسيط
        const notification = document.createElement('div');
        notification.className = `fixed top-4 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-lg text-white font-semibold z-50 transition-all duration-300`;
        
        const colors = {
            success: 'bg-green-500',
            warning: 'bg-yellow-500',
            error: 'bg-red-500',
            info: 'bg-blue-500'
        };
        
        notification.classList.add(colors[type]);
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translate(-50%, -100%)';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new CollaborativeWriting();
});
