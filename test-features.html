<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الميزات الجديدة - ركن الأطفال</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .test-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .success {
            background: #10b981;
        }

        .error {
            background: #ef4444;
        }

        .warning {
            background: #f59e0b;
        }

        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: 500;
        }

        .result.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }

        .result.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .result.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-white text-center mb-8">
            🧪 اختبار الميزات الجديدة
        </h1>

        <!-- Test Navigation -->
        <div class="test-card">
            <h2 class="text-xl font-bold mb-4">🔗 اختبار التنقل</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button class="test-button" onclick="testNavigation('index.html')">
                    <i class="fas fa-home ml-2"></i>
                    الصفحة الرئيسية
                </button>
                <button class="test-button" onclick="testNavigation('interactive-story-maker.html')">
                    <i class="fas fa-magic ml-2"></i>
                    صانع القصص
                </button>
                <button class="test-button" onclick="testNavigation('collaborative-writing.html')">
                    <i class="fas fa-edit ml-2"></i>
                    الكتابة التعاونية
                </button>
                <button class="test-button" onclick="testNavigation('advanced-customization.html')">
                    <i class="fas fa-cog ml-2"></i>
                    التخصيص المتقدم
                </button>
            </div>
            <div id="navigation-results"></div>
        </div>

        <!-- Test Data Collection -->
        <div class="test-card">
            <h2 class="text-xl font-bold mb-4">📊 اختبار جمع البيانات</h2>
            <div class="mb-4">
                <label class="block mb-2">اسم الطفل:</label>
                <input type="text" id="test-child-name" class="w-full p-2 border rounded" placeholder="أحمد">
            </div>
            <div class="mb-4">
                <label class="block mb-2">العمر:</label>
                <input type="number" id="test-child-age" class="w-full p-2 border rounded" placeholder="7">
            </div>
            <div class="mb-4">
                <label class="block mb-2">النوع:</label>
                <select id="test-child-gender" class="w-full p-2 border rounded">
                    <option value="boy">ولد</option>
                    <option value="girl">بنت</option>
                </select>
            </div>
            <div class="mb-4">
                <label class="block mb-2">عناصر القصة:</label>
                <div class="grid grid-cols-2 gap-2">
                    <label><input type="checkbox" name="elements" value="animals"> حيوانات</label>
                    <label><input type="checkbox" name="elements" value="adventure"> مغامرة</label>
                    <label><input type="checkbox" name="elements" value="magic"> سحر</label>
                    <label><input type="checkbox" name="elements" value="friendship"> صداقة</label>
                </div>
            </div>
            <button class="test-button" onclick="testDataCollection()">
                <i class="fas fa-database ml-2"></i>
                اختبار جمع البيانات
            </button>
            <div id="data-results"></div>
        </div>

        <!-- Test Local Storage -->
        <div class="test-card">
            <h2 class="text-xl font-bold mb-4">💾 اختبار التخزين المحلي</h2>
            <button class="test-button" onclick="testSaveData()">
                <i class="fas fa-save ml-2"></i>
                حفظ بيانات تجريبية
            </button>
            <button class="test-button" onclick="testLoadData()">
                <i class="fas fa-download ml-2"></i>
                تحميل البيانات
            </button>
            <button class="test-button" onclick="testClearData()">
                <i class="fas fa-trash ml-2"></i>
                مسح البيانات
            </button>
            <div id="storage-results"></div>
        </div>

        <!-- Test Story Generation -->
        <div class="test-card">
            <h2 class="text-xl font-bold mb-4">📖 اختبار توليد القصص</h2>
            <div class="mb-4">
                <label class="block mb-2">نص المدخل:</label>
                <textarea id="test-input" class="w-full p-2 border rounded" rows="3" placeholder="طفل يحب الحيوانات يجد قطة صغيرة في الحديقة"></textarea>
            </div>
            <button class="test-button" onclick="testStoryGeneration()">
                <i class="fas fa-magic ml-2"></i>
                توليد قصة تجريبية
            </button>
            <div id="story-results"></div>
        </div>

        <!-- Test Responsive Design -->
        <div class="test-card">
            <h2 class="text-xl font-bold mb-4">📱 اختبار التصميم المتجاوب</h2>
            <button class="test-button" onclick="testResponsive('mobile')">
                <i class="fas fa-mobile ml-2"></i>
                عرض الجوال
            </button>
            <button class="test-button" onclick="testResponsive('tablet')">
                <i class="fas fa-tablet ml-2"></i>
                عرض التابلت
            </button>
            <button class="test-button" onclick="testResponsive('desktop')">
                <i class="fas fa-desktop ml-2"></i>
                عرض سطح المكتب
            </button>
            <div id="responsive-results"></div>
        </div>

        <!-- Overall Results -->
        <div class="test-card">
            <h2 class="text-xl font-bold mb-4">📋 النتائج الإجمالية</h2>
            <button class="test-button" onclick="runAllTests()">
                <i class="fas fa-play ml-2"></i>
                تشغيل جميع الاختبارات
            </button>
            <div id="overall-results"></div>
        </div>
    </div>

    <script>
        let testResults = {
            navigation: 0,
            dataCollection: 0,
            localStorage: 0,
            storyGeneration: 0,
            responsive: 0
        };

        function showResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'exclamation'} ml-2"></i>
                ${message}
            `;
            container.appendChild(result);
        }

        function testNavigation(url) {
            try {
                // محاولة فتح الصفحة في نافذة جديدة
                const newWindow = window.open(url, '_blank');
                if (newWindow) {
                    showResult('navigation-results', `تم فتح ${url} بنجاح`, 'success');
                    testResults.navigation++;
                    setTimeout(() => newWindow.close(), 3000);
                } else {
                    showResult('navigation-results', `فشل في فتح ${url}`, 'error');
                }
            } catch (error) {
                showResult('navigation-results', `خطأ في التنقل: ${error.message}`, 'error');
            }
        }

        function testDataCollection() {
            try {
                const data = {
                    childName: document.getElementById('test-child-name').value,
                    childAge: document.getElementById('test-child-age').value,
                    childGender: document.getElementById('test-child-gender').value,
                    elements: Array.from(document.querySelectorAll('input[name="elements"]:checked')).map(el => el.value)
                };

                if (data.childName && data.childAge) {
                    showResult('data-results', `تم جمع البيانات بنجاح: ${JSON.stringify(data, null, 2)}`, 'success');
                    testResults.dataCollection++;
                } else {
                    showResult('data-results', 'يرجى ملء جميع الحقول المطلوبة', 'warning');
                }
            } catch (error) {
                showResult('data-results', `خطأ في جمع البيانات: ${error.message}`, 'error');
            }
        }

        function testSaveData() {
            try {
                const testData = {
                    mode: 'test',
                    timestamp: new Date().toISOString(),
                    userInputs: {
                        childName: 'أحمد التجريبي',
                        childAge: '7',
                        preferences: {
                            storyTone: 'exciting',
                            storyLength: 'medium'
                        }
                    }
                };

                localStorage.setItem('collaborativeStoryData', JSON.stringify(testData));
                showResult('storage-results', 'تم حفظ البيانات التجريبية بنجاح', 'success');
                testResults.localStorage++;
            } catch (error) {
                showResult('storage-results', `خطأ في الحفظ: ${error.message}`, 'error');
            }
        }

        function testLoadData() {
            try {
                const data = localStorage.getItem('collaborativeStoryData');
                if (data) {
                    const parsedData = JSON.parse(data);
                    showResult('storage-results', `تم تحميل البيانات: ${JSON.stringify(parsedData, null, 2)}`, 'success');
                    testResults.localStorage++;
                } else {
                    showResult('storage-results', 'لا توجد بيانات محفوظة', 'warning');
                }
            } catch (error) {
                showResult('storage-results', `خطأ في التحميل: ${error.message}`, 'error');
            }
        }

        function testClearData() {
            try {
                localStorage.removeItem('collaborativeStoryData');
                showResult('storage-results', 'تم مسح البيانات بنجاح', 'success');
            } catch (error) {
                showResult('storage-results', `خطأ في المسح: ${error.message}`, 'error');
            }
        }

        function testStoryGeneration() {
            try {
                const input = document.getElementById('test-input').value;
                if (!input) {
                    showResult('story-results', 'يرجى إدخال نص للاختبار', 'warning');
                    return;
                }

                // محاكاة تحسين النص
                const enhanced = enhanceTestInput(input);
                showResult('story-results', `النص المحسن: ${enhanced}`, 'success');
                testResults.storyGeneration++;
            } catch (error) {
                showResult('story-results', `خطأ في توليد القصة: ${error.message}`, 'error');
            }
        }

        function enhanceTestInput(input) {
            // محاكاة تحسين النص
            let enhanced = input;
            
            // إضافة افتتاحية عربية
            if (!enhanced.startsWith('كان يا ما كان')) {
                enhanced = `كان يا ما كان، ${enhanced}`;
            }
            
            // إضافة توجيهات صوتية
            enhanced += '\n\n*(بصوت مرح ومتحمس)*';
            
            // إضافة خاتمة
            enhanced += '\n\nوهكذا بدأت مغامرة جميلة...';
            
            return enhanced;
        }

        function testResponsive(size) {
            try {
                const sizes = {
                    mobile: { width: 375, height: 667 },
                    tablet: { width: 768, height: 1024 },
                    desktop: { width: 1920, height: 1080 }
                };

                const targetSize = sizes[size];
                if (targetSize) {
                    // محاولة تغيير حجم النافذة (قد لا يعمل في جميع المتصفحات)
                    window.resizeTo(targetSize.width, targetSize.height);
                    showResult('responsive-results', `تم اختبار عرض ${size}: ${targetSize.width}x${targetSize.height}`, 'success');
                    testResults.responsive++;
                } else {
                    showResult('responsive-results', `حجم غير معروف: ${size}`, 'error');
                }
            } catch (error) {
                showResult('responsive-results', `خطأ في اختبار التجاوب: ${error.message}`, 'warning');
            }
        }

        function runAllTests() {
            // مسح النتائج السابقة
            testResults = {
                navigation: 0,
                dataCollection: 0,
                localStorage: 0,
                storyGeneration: 0,
                responsive: 0
            };

            // تشغيل جميع الاختبارات
            setTimeout(() => {
                testNavigation('index.html');
                testDataCollection();
                testSaveData();
                testLoadData();
                testStoryGeneration();
                testResponsive('desktop');

                // عرض النتائج الإجمالية
                setTimeout(() => {
                    const total = Object.values(testResults).reduce((a, b) => a + b, 0);
                    const maxTests = 6;
                    const percentage = Math.round((total / maxTests) * 100);
                    
                    showResult('overall-results', 
                        `اكتملت الاختبارات: ${total}/${maxTests} (${percentage}%)`, 
                        percentage > 80 ? 'success' : percentage > 50 ? 'warning' : 'error'
                    );
                }, 2000);
            }, 500);
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            showResult('overall-results', 'تم تحميل صفحة الاختبار بنجاح', 'success');
        });
    </script>
</body>
</html>
