// Bedtime Story Generator Application
class BedtimeStoryGenerator {
    constructor() {
        this.currentStep = 0;
        this.totalSteps = 6;
        this.formData = {
            age: '',
            gender: '',
            interests: [],
            style: '',
            lesson: '',
            additionalDetails: ''
        };

        this.steps = [
            { id: 'age', title: 'عمر الطفل', icon: 'fas fa-birthday-cake' },
            { id: 'gender', title: 'جنس الطفل', icon: 'fas fa-venus-mars' },
            { id: 'interests', title: 'الاهتمامات', icon: 'fas fa-star' },
            { id: 'style', title: 'أسلوب القصة', icon: 'fas fa-magic' },
            { id: 'lesson', title: 'الدرس المستفاد', icon: 'fas fa-lightbulb' },
            { id: 'additional', title: 'تفاصيل إضافية', icon: 'fas fa-plus-circle' }
        ];

        // تهيئة موارد القصص المحسنة
        this.storyResources = new StoryResources();

        this.init();
    }

    init() {
        this.bindEvents();
        this.setupFormSteps();
    }

    bindEvents() {
        // Start button
        document.getElementById('start-btn').addEventListener('click', () => {
            this.showFormSection();
        });

        // Navigation buttons
        document.getElementById('next-btn').addEventListener('click', () => {
            this.nextStep();
        });

        document.getElementById('prev-btn').addEventListener('click', () => {
            this.prevStep();
        });

        // Age selection
        document.querySelectorAll('.age-option').forEach(button => {
            button.addEventListener('click', (e) => {
                this.selectAge(e.target.closest('.age-option').dataset.value);
            });
        });
    }

    showFormSection() {
        document.getElementById('welcome-section').classList.add('hidden');
        document.getElementById('form-section').classList.remove('hidden');
        this.updateProgress();
    }

    selectAge(age) {
        this.formData.age = age;

        // Update UI
        document.querySelectorAll('.age-option').forEach(btn => {
            btn.classList.remove('selected');
        });
        document.querySelector(`[data-value="${age}"]`).classList.add('selected');

        // Enable next button
        document.getElementById('next-btn').disabled = false;
    }

    nextStep() {
        if (this.currentStep < this.totalSteps - 1) {
            this.currentStep++;
            this.showStep(this.currentStep);
            this.updateProgress();
            this.updateNavigation();
        } else {
            this.generateStory();
        }
    }

    prevStep() {
        if (this.currentStep > 0) {
            this.currentStep--;
            this.showStep(this.currentStep);
            this.updateProgress();
            this.updateNavigation();
        }
    }

    showStep(stepIndex) {
        // Hide all steps
        document.querySelectorAll('.step-content').forEach(step => {
            step.classList.add('hidden');
        });

        // Show current step
        const currentStepElement = document.getElementById(`step-${stepIndex + 1}`);
        if (currentStepElement) {
            currentStepElement.classList.remove('hidden');
        } else {
            this.createStep(stepIndex);
        }
    }

    createStep(stepIndex) {
        const stepData = this.steps[stepIndex];
        const container = document.querySelector('.glass-effect');

        // Clear existing content
        container.innerHTML = '';

        let stepHTML = '';

        switch (stepData.id) {
            case 'gender':
                stepHTML = this.createGenderStep();
                break;
            case 'interests':
                stepHTML = this.createInterestsStep();
                break;
            case 'style':
                stepHTML = this.createStyleStep();
                break;
            case 'lesson':
                stepHTML = this.createLessonStep();
                break;
            case 'additional':
                stepHTML = this.createAdditionalStep();
                break;
            default:
                stepHTML = this.createAgeStep();
        }

        container.innerHTML = stepHTML;
        this.bindStepEvents(stepData.id);
    }

    createGenderStep() {
        return `
            <div id="step-2" class="step-content">
                <h2 class="text-3xl font-bold text-white mb-8 text-center arabic-text">
                    <i class="fas fa-venus-mars ml-3 text-yellow-300"></i>
                    اختر جنس الطفل
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <button class="gender-option option-button p-6 rounded-xl text-white transition-all duration-300" data-value="boy">
                        <i class="fas fa-mars text-3xl mb-3 text-blue-300"></i>
                        <div class="text-lg font-semibold arabic-text">ولد</div>
                    </button>
                    <button class="gender-option option-button p-6 rounded-xl text-white transition-all duration-300" data-value="girl">
                        <i class="fas fa-venus text-3xl mb-3 text-pink-300"></i>
                        <div class="text-lg font-semibold arabic-text">بنت</div>
                    </button>
                    <button class="gender-option option-button p-6 rounded-xl text-white transition-all duration-300" data-value="non-binary">
                        <i class="fas fa-genderless text-3xl mb-3 text-purple-300"></i>
                        <div class="text-lg font-semibold arabic-text">غير محدد</div>
                    </button>
                    <button class="gender-option option-button p-6 rounded-xl text-white transition-all duration-300" data-value="any">
                        <i class="fas fa-users text-3xl mb-3 text-green-300"></i>
                        <div class="text-lg font-semibold arabic-text">أي جنس</div>
                    </button>
                </div>
            </div>
        `;
    }

    createInterestsStep() {
        const interests = [
            { value: 'animals', label: 'الحيوانات', icon: 'fas fa-paw', color: 'text-green-300' },
            { value: 'space', label: 'الفضاء والنجوم', icon: 'fas fa-rocket', color: 'text-blue-300' },
            { value: 'adventure', label: 'المغامرات', icon: 'fas fa-map', color: 'text-orange-300' },
            { value: 'magic', label: 'السحر والخيال', icon: 'fas fa-magic', color: 'text-purple-300' },
            { value: 'friendship', label: 'الصداقة', icon: 'fas fa-heart', color: 'text-pink-300' },
            { value: 'nature', label: 'الطبيعة', icon: 'fas fa-tree', color: 'text-green-400' },
            { value: 'ocean', label: 'البحر والمحيط', icon: 'fas fa-fish', color: 'text-blue-400' },
            { value: 'dinosaurs', label: 'الديناصورات', icon: 'fas fa-dragon', color: 'text-red-300' },
            { value: 'vehicles', label: 'السيارات والمركبات', icon: 'fas fa-car', color: 'text-gray-300' },
            { value: 'music', label: 'الموسيقى والرقص', icon: 'fas fa-music', color: 'text-yellow-300' }
        ];

        let interestsHTML = interests.map(interest => `
            <button class="interest-option option-button p-4 rounded-xl text-white transition-all duration-300" data-value="${interest.value}">
                <i class="${interest.icon} text-2xl mb-2 ${interest.color}"></i>
                <div class="text-sm font-semibold arabic-text">${interest.label}</div>
            </button>
        `).join('');

        return `
            <div id="step-3" class="step-content">
                <h2 class="text-3xl font-bold text-white mb-4 text-center arabic-text">
                    <i class="fas fa-star ml-3 text-yellow-300"></i>
                    ما هي اهتمامات طفلك؟
                </h2>
                <p class="text-purple-200 mb-8 text-center arabic-text">يمكنك اختيار حتى 3 اهتمامات</p>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
                    ${interestsHTML}
                </div>
                <div id="selected-interests" class="mt-6 text-center">
                    <div class="text-white mb-2 arabic-text">الاهتمامات المختارة:</div>
                    <div id="interests-display" class="flex flex-wrap justify-center gap-2"></div>
                </div>
            </div>
        `;
    }

    updateProgress() {
        const percentage = Math.round(((this.currentStep + 1) / this.totalSteps) * 100);
        document.getElementById('current-step').textContent = this.currentStep + 1;
        document.getElementById('progress-percentage').textContent = percentage;
        document.getElementById('progress-bar').style.width = `${percentage}%`;
    }

    updateNavigation() {
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');

        // Show/hide previous button
        if (this.currentStep > 0) {
            prevBtn.classList.remove('hidden');
        } else {
            prevBtn.classList.add('hidden');
        }

        // Update next button text
        if (this.currentStep === this.totalSteps - 1) {
            nextBtn.innerHTML = '<i class="fas fa-magic ml-2"></i> إنشاء القصة';
            nextBtn.className = 'bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-xl font-medium hover:from-purple-700 hover:to-pink-700 transition-all duration-300 button-glow arabic-text';
        } else {
            nextBtn.innerHTML = 'التالي <i class="fas fa-arrow-left mr-2"></i>';
            nextBtn.className = 'bg-gradient-to-r from-green-500 to-blue-500 text-white px-8 py-3 rounded-xl font-medium hover:from-green-600 hover:to-blue-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed button-glow arabic-text';
        }
    }

    createStyleStep() {
        const styles = [
            { value: 'funny', label: 'مضحك ومرح', icon: 'fas fa-laugh', color: 'text-yellow-300', desc: 'قصص مليئة بالضحك والمرح' },
            { value: 'gentle', label: 'هادئ ولطيف', icon: 'fas fa-dove', color: 'text-blue-300', desc: 'قصص مريحة ومهدئة' },
            { value: 'exciting', label: 'مثير ومشوق', icon: 'fas fa-bolt', color: 'text-orange-300', desc: 'مغامرات مثيرة ومشوقة' },
            { value: 'magical', label: 'سحري وخيالي', icon: 'fas fa-sparkles', color: 'text-purple-300', desc: 'عالم من السحر والخيال' }
        ];

        let stylesHTML = styles.map(style => `
            <button class="style-option option-button p-6 rounded-xl text-white transition-all duration-300" data-value="${style.value}">
                <i class="${style.icon} text-4xl mb-3 ${style.color}"></i>
                <div class="text-lg font-semibold arabic-text mb-2">${style.label}</div>
                <div class="text-sm text-purple-200 arabic-text">${style.desc}</div>
            </button>
        `).join('');

        return `
            <div id="step-4" class="step-content">
                <h2 class="text-3xl font-bold text-white mb-8 text-center arabic-text">
                    <i class="fas fa-magic ml-3 text-yellow-300"></i>
                    ما هو أسلوب القصة المفضل؟
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    ${stylesHTML}
                </div>
            </div>
        `;
    }

    createLessonStep() {
        const lessons = [
            { value: 'kindness', label: 'اللطف والرحمة', icon: 'fas fa-heart' },
            { value: 'sharing', label: 'المشاركة والكرم', icon: 'fas fa-hands-helping' },
            { value: 'courage', label: 'الشجاعة والجرأة', icon: 'fas fa-shield-alt' },
            { value: 'honesty', label: 'الصدق والأمانة', icon: 'fas fa-handshake' },
            { value: 'perseverance', label: 'المثابرة وعدم الاستسلام', icon: 'fas fa-mountain' },
            { value: 'friendship', label: 'الصداقة والتعاون', icon: 'fas fa-users' },
            { value: 'responsibility', label: 'المسؤولية والالتزام', icon: 'fas fa-balance-scale' },
            { value: 'empathy', label: 'التعاطف وفهم الآخرين', icon: 'fas fa-hands-heart' },
            { value: 'confidence', label: 'الثقة بالنفس', icon: 'fas fa-star' },
            { value: 'gratitude', label: 'الامتنان والشكر', icon: 'fas fa-gift' }
        ];

        let lessonsHTML = lessons.map(lesson => `
            <button class="lesson-option option-button p-4 rounded-xl text-white transition-all duration-300" data-value="${lesson.value}">
                <i class="${lesson.icon} text-2xl mb-2 text-yellow-300"></i>
                <div class="text-sm font-semibold arabic-text">${lesson.label}</div>
            </button>
        `).join('');

        return `
            <div id="step-5" class="step-content">
                <h2 class="text-3xl font-bold text-white mb-8 text-center arabic-text">
                    <i class="fas fa-lightbulb ml-3 text-yellow-300"></i>
                    ما هو الدرس الذي تريد تعليمه؟
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    ${lessonsHTML}
                </div>
            </div>
        `;
    }

    createAdditionalStep() {
        return `
            <div id="step-6" class="step-content">
                <h2 class="text-3xl font-bold text-white mb-8 text-center arabic-text">
                    <i class="fas fa-plus-circle ml-3 text-yellow-300"></i>
                    هل هناك تفاصيل إضافية؟
                </h2>
                <div class="max-w-2xl mx-auto">
                    <p class="text-purple-200 mb-6 text-center arabic-text">
                        أخبرنا عن أي تفاصيل خاصة تريد إضافتها للقصة (اختياري)
                    </p>
                    <textarea
                        id="additional-details"
                        class="w-full p-4 rounded-xl bg-white/10 border-2 border-white/30 text-white placeholder-purple-200 focus:border-yellow-400 focus:outline-none transition-all duration-300 arabic-text"
                        rows="6"
                        placeholder="مثال: طفلي يحب التنانين الودودة، أو يريد أن تكون القصة عن طفل يساعد جدته..."
                        style="resize: vertical; direction: rtl;"
                    ></textarea>
                    <div class="mt-4 text-sm text-purple-200 arabic-text">
                        <i class="fas fa-info-circle ml-2"></i>
                        هذه الخطوة اختيارية - يمكنك تركها فارغة والمتابعة
                    </div>
                </div>
            </div>
        `;
    }

    bindStepEvents(stepId) {
        switch (stepId) {
            case 'gender':
                document.querySelectorAll('.gender-option').forEach(button => {
                    button.addEventListener('click', (e) => {
                        this.selectGender(e.target.closest('.gender-option').dataset.value);
                    });
                });
                break;
            case 'interests':
                document.querySelectorAll('.interest-option').forEach(button => {
                    button.addEventListener('click', (e) => {
                        this.toggleInterest(e.target.closest('.interest-option').dataset.value);
                    });
                });
                break;
            case 'style':
                document.querySelectorAll('.style-option').forEach(button => {
                    button.addEventListener('click', (e) => {
                        this.selectStyle(e.target.closest('.style-option').dataset.value);
                    });
                });
                break;
            case 'lesson':
                document.querySelectorAll('.lesson-option').forEach(button => {
                    button.addEventListener('click', (e) => {
                        this.selectLesson(e.target.closest('.lesson-option').dataset.value);
                    });
                });
                break;
            case 'additional':
                document.getElementById('additional-details').addEventListener('input', (e) => {
                    this.formData.additionalDetails = e.target.value;
                    document.getElementById('next-btn').disabled = false;
                });
                // Enable next button immediately for optional step
                document.getElementById('next-btn').disabled = false;
                break;
        }
    }

    selectGender(gender) {
        this.formData.gender = gender;
        this.updateSelection('.gender-option', gender);
        document.getElementById('next-btn').disabled = false;
    }

    toggleInterest(interest) {
        const interests = this.formData.interests;
        const index = interests.indexOf(interest);

        if (index > -1) {
            interests.splice(index, 1);
        } else if (interests.length < 3) {
            interests.push(interest);
        }

        this.updateInterestsDisplay();
        document.getElementById('next-btn').disabled = interests.length === 0;
    }

    updateInterestsDisplay() {
        const container = document.getElementById('interests-display');
        const interests = this.formData.interests;

        // Update button states
        document.querySelectorAll('.interest-option').forEach(button => {
            const value = button.dataset.value;
            if (interests.includes(value)) {
                button.classList.add('selected');
            } else {
                button.classList.remove('selected');
            }

            // Disable if max reached and not selected
            if (interests.length >= 3 && !interests.includes(value)) {
                button.style.opacity = '0.5';
                button.style.pointerEvents = 'none';
            } else {
                button.style.opacity = '1';
                button.style.pointerEvents = 'auto';
            }
        });

        // Update display
        container.innerHTML = interests.map(interest => {
            const button = document.querySelector(`[data-value="${interest}"]`);
            const label = button.querySelector('.arabic-text').textContent;
            return `<span class="interest-tag selected">${label}</span>`;
        }).join('');
    }

    selectStyle(style) {
        this.formData.style = style;
        this.updateSelection('.style-option', style);
        document.getElementById('next-btn').disabled = false;
    }

    selectLesson(lesson) {
        this.formData.lesson = lesson;
        this.updateSelection('.lesson-option', lesson);
        document.getElementById('next-btn').disabled = false;
    }

    updateSelection(selector, value) {
        document.querySelectorAll(selector).forEach(btn => {
            btn.classList.remove('selected');
        });
        document.querySelector(`${selector}[data-value="${value}"]`).classList.add('selected');
    }

    async generateStory() {
        // Hide form and show loading
        document.getElementById('form-section').classList.add('hidden');
        this.showLoadingScreen();

        try {
            // Generate the story
            const story = await this.createPersonalizedStory();

            // Show the story
            this.displayStory(story);
        } catch (error) {
            console.error('Error generating story:', error);
            this.showError('عذراً! حدثت مشكلة في إنشاء قصتك. يرجى المحاولة مرة أخرى.');
        }
    }

    showLoadingScreen() {
        const storySection = document.getElementById('story-section');
        storySection.innerHTML = `
            <div class="glass-effect rounded-3xl p-12 shadow-2xl text-center">
                <div class="loading-spinner mx-auto mb-6"></div>
                <h2 class="text-3xl font-bold text-white mb-4 arabic-text">
                    جاري نسج قصة أحلامك السحرية<span class="loading-dots"></span>
                </h2>
                <p class="text-xl text-purple-200 arabic-text">
                    ننشئ حكاية جميلة خاصة بطفلك الصغير
                </p>
                <div class="mt-8 text-purple-300 arabic-text">
                    <i class="fas fa-sparkles ml-2"></i>
                    يرجى الانتظار قليلاً...
                </div>
            </div>
        `;
        storySection.classList.remove('hidden');
    }

    async createPersonalizedStory() {
        // This is where you would integrate with an AI API like Claude or GPT
        // For now, we'll create a sophisticated story generation system

        const storyElements = this.getStoryElements();
        const story = this.generateStoryContent(storyElements);

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        return story;
    }

    getStoryElements() {
        const { age, gender, interests, style, lesson, additionalDetails } = this.formData;

        // Character names based on gender
        const characterNames = {
            boy: ['أحمد', 'محمد', 'عمر', 'يوسف', 'خالد', 'سعد'],
            girl: ['فاطمة', 'عائشة', 'زينب', 'مريم', 'سارة', 'نور'],
            'non-binary': ['الطفل الصغير', 'البطل الشجاع', 'المستكشف الصغير'],
            any: ['أحمد', 'فاطمة', 'عمر', 'زينب', 'محمد', 'سارة']
        };

        const protagonist = characterNames[gender][Math.floor(Math.random() * characterNames[gender].length)];

        // Story settings based on interests
        const settings = {
            animals: 'في غابة جميلة مليئة بالحيوانات الودودة',
            space: 'في رحلة مثيرة عبر النجوم والكواكب',
            adventure: 'في مغامرة شيقة في أرض بعيدة',
            magic: 'في مملكة سحرية مليئة بالعجائب',
            friendship: 'في قرية صغيرة مع أصدقاء رائعين',
            nature: 'في حديقة خضراء جميلة',
            ocean: 'في أعماق البحر الأزرق الساحر',
            dinosaurs: 'في عالم الديناصورات الودودة',
            vehicles: 'في مدينة مليئة بالسيارات الذكية',
            music: 'في عالم الموسيقى والألحان الجميلة'
        };

        const mainSetting = settings[interests[0]] || 'في مكان جميل وساحر';

        return {
            protagonist,
            age,
            gender,
            interests,
            style,
            lesson,
            additionalDetails,
            setting: mainSetting
        };
    }

    generateStoryContent(elements) {
        const { protagonist, age, interests, style, lesson, setting, additionalDetails } = elements;

        // استخدام الموارد المحسنة للحصول على محتوى متنوع وثري
        const storyOpening = this.storyResources.getRandomOpening();
        const storyEnding = this.storyResources.getRandomEnding();

        // تخصيص القصة حسب العمر
        const ageGroup = age <= 5 ? 'young' : age <= 8 ? 'middle' : 'older';
        const appropriateLesson = this.storyResources.getAgeAppropriateLesson(age);

        // اختيار شخصيات وبيئات محسنة
        const enhancedSetting = this.storyResources.getRandomSetting();
        const supportingCharacter = this.storyResources.getRandomCharacter('animals');

        const opening = openings[style] || openings.gentle;

        // Generate linguistically accurate story content with gender-aware grammar
        // إنشاء محتوى القصة مع مراعاة التذكير والتأنيث والإعراب
        const storyContent = this.buildLinguisticallyAccurateNarrative(elements);

        // Apply comprehensive linguistic review for grammar, spelling, and style
        // تطبيق مراجعة لغوية شاملة للنحو والإملاء والأسلوب
        const reviewedContent = this.performLinguisticReview(`${opening}، ${storyContent}`);

        // Create the complete story with proper Arabic structure
        const story = {
            title: this.generateGrammaticallyCorrectTitle(elements),
            content: reviewedContent,
            moral: this.getMoralMessage(lesson),
            elements: elements
        };

        return story;
    }

    generateGrammaticallyCorrectTitle(elements) {
        const { protagonist, interests, style, gender } = elements;

        // Gender-aware title templates with proper Arabic grammar
        const titleTemplates = {
            funny: {
                boy: `مغامراتُ ${protagonist} المضحكةُ`,
                girl: `مغامراتُ ${protagonist} المضحكةُ`
            },
            gentle: {
                boy: `حكايةُ ${protagonist} الجميلةُ`,
                girl: `حكايةُ ${protagonist} الجميلةُ`
            },
            exciting: {
                boy: `رحلةُ ${protagonist} المثيرةُ`,
                girl: `رحلةُ ${protagonist} المثيرةُ`
            },
            magical: {
                boy: `سحرُ ${protagonist} العجيبُ`,
                girl: `سحرُ ${protagonist} العجيبُ`
            }
        };

        const genderKey = gender === 'boy' ? 'boy' : 'girl';
        return titleTemplates[style]?.[genderKey] || `قصةُ ${protagonist}`;
    }

    buildLinguisticallyAccurateNarrative(elements) {
        const { protagonist, age, gender, interests, style, lesson, setting, additionalDetails } = elements;

        // Build grammatically correct story with proper gender agreement
        let narrative = this.createGenderAwareIntroduction(protagonist, gender, setting);

        // Add character traits with proper grammar
        narrative += this.addCharacterTraitsWithCorrectGrammar(protagonist, gender, interests);

        // Add the main conflict/challenge with linguistic accuracy
        narrative += this.getGrammaticallyCorrectConflict(elements);

        // Add the resolution that teaches the lesson with proper Arabic
        narrative += this.getGrammaticallyCorrectResolution(elements);

        // Add additional details if provided with proper integration
        if (additionalDetails) {
            narrative += ` ${this.integrateAdditionalDetails(additionalDetails, gender)}`;
        }

        // Add peaceful ending with correct grammar
        narrative += this.getGrammaticallyCorrectPeacefulEnding(elements);

        return narrative;
    }

    createGenderAwareIntroduction(protagonist, gender, setting) {
        if (gender === 'boy') {
            return `${setting}، كان هناك طفلٌ صغيرٌ اسمُه ${protagonist}. `;
        } else if (gender === 'girl') {
            return `${setting}، كانت هناك طفلةٌ صغيرةٌ اسمُها ${protagonist}. `;
        } else {
            return `${setting}، كان هناك طفلٌ اسمُه ${protagonist}. `;
        }
    }

    addCharacterTraitsWithCorrectGrammar(protagonist, gender, interests) {
        let traits = '';
        const wasVerb = gender === 'girl' ? 'كانت' : 'كان';
        const pronounHe = gender === 'girl' ? 'هي' : 'هو';
        const verbEnding = gender === 'girl' ? 'تُ' : '';

        if (interests.includes('animals')) {
            if (gender === 'girl') {
                traits += `كانت ${protagonist} تُحبُّ الحيواناتِ كثيراً وتتحدثُ معها كأنها صديقاتُها المقرَّباتُ. `;
            } else {
                traits += `كان ${protagonist} يُحبُّ الحيواناتِ كثيراً ويتحدثُ معها كأنها أصدقاؤُه المقرَّبون. `;
            }
        }

        if (interests.includes('space')) {
            if (gender === 'girl') {
                traits += `كانت ${protagonist} مولعةً بالنجوم والكواكبِ، وتحلمُ بأن تُصبحَ رائدةَ فضاءٍ يوماً ما. `;
            } else {
                traits += `كان ${protagonist} مولعاً بالنجوم والكواكبِ، ويحلمُ بأن يُصبحَ رائدَ فضاءٍ يوماً ما. `;
            }
        }

        if (interests.includes('magic')) {
            if (gender === 'girl') {
                traits += `كانت ${protagonist} تُؤمنُ بالسحر والعجائبِ، وترى الجمالَ في كلِّ شيءٍ حولَها. `;
            } else {
                traits += `كان ${protagonist} يُؤمنُ بالسحر والعجائبِ، ويرى الجمالَ في كلِّ شيءٍ حولَه. `;
            }
        }

        if (interests.includes('adventure')) {
            if (gender === 'girl') {
                traits += `كانت ${protagonist} تُحبُّ المغامراتِ والاستكشافَ، وتبحثُ عن التجارب الجديدةِ. `;
            } else {
                traits += `كان ${protagonist} يُحبُّ المغامراتِ والاستكشافَ، ويبحثُ عن التجارب الجديدةِ. `;
            }
        }

        return traits;
    }

    getGrammaticallyCorrectConflict(elements) {
        const { protagonist, interests, lesson, gender } = elements;

        const conflicts = {
            kindness: gender === 'girl'
                ? `في يومٍ من الأيام، رأت ${protagonist} حيواناً صغيراً يبكي لأن أحداً لم يكن لطيفاً معه. `
                : `في يومٍ من الأيام، رأى ${protagonist} حيواناً صغيراً يبكي لأن أحداً لم يكن لطيفاً معه. `,

            sharing: gender === 'girl'
                ? `وجدت ${protagonist} كنزاً جميلاً، لكنها تذكَّرت صديقاتِها اللواتي لم يجدن شيئاً. `
                : `وجد ${protagonist} كنزاً جميلاً، لكنه تذكَّر أصدقاءَه الذين لم يجدوا شيئاً. `,

            courage: gender === 'girl'
                ? `واجهت ${protagonist} تحدياً كبيراً جعلها تشعرُ بالخوف في البداية. `
                : `واجه ${protagonist} تحدياً كبيراً جعله يشعرُ بالخوف في البداية. `,

            honesty: gender === 'girl'
                ? `وقعت ${protagonist} في موقفٍ صعبٍ حيث كان عليها أن تختارَ بين قول الحقيقة أو إخفائها. `
                : `وقع ${protagonist} في موقفٍ صعبٍ حيث كان عليه أن يختارَ بين قول الحقيقة أو إخفائها. `,

            perseverance: gender === 'girl'
                ? `حاولت ${protagonist} تعلُّمَ شيءٍ جديدٍ، لكنها فشلت في المحاولات الأولى. `
                : `حاول ${protagonist} تعلُّمَ شيءٍ جديدٍ، لكنه فشل في المحاولات الأولى. `,

            friendship: gender === 'girl'
                ? `التقت ${protagonist} بطفلةٍ جديدةٍ كانت تبدو مختلفةً عن الأخريات. `
                : `التقى ${protagonist} بطفلٍ جديدٍ كان يبدو مختلفاً عن الآخرين. `,

            responsibility: gender === 'girl'
                ? `أُوكلت إلى ${protagonist} مهمةٌ مهمةٌ، لكنها كانت تريدُ اللعبَ بدلاً من ذلك. `
                : `أُوكلت إلى ${protagonist} مهمةٌ مهمةٌ، لكنه كان يريدُ اللعبَ بدلاً من ذلك. `,

            empathy: gender === 'girl'
                ? `رأت ${protagonist} صديقتَها حزينةً ولم تعرف كيف تساعدُها. `
                : `رأى ${protagonist} صديقَه حزيناً ولم يعرف كيف يساعدُه. `,

            confidence: gender === 'girl'
                ? `شكَّت ${protagonist} في قدراتِها عندما واجهت مهمةً جديدةً. `
                : `شكَّ ${protagonist} في قدراتِه عندما واجه مهمةً جديدةً. `,

            gratitude: gender === 'girl'
                ? `كانت ${protagonist} تأخذُ كلَّ النعم من حولِها كأمرٍ مسلَّمٍ به. `
                : `كان ${protagonist} يأخذُ كلَّ النعم من حولِه كأمرٍ مسلَّمٍ به. `
        };

        return conflicts[lesson] || (gender === 'girl'
            ? `واجهت ${protagonist} تحدياً جعلها تتعلمُ درساً مهماً. `
            : `واجه ${protagonist} تحدياً جعله يتعلمُ درساً مهماً. `);
    }

    getGrammaticallyCorrectResolution(elements) {
        const { protagonist, lesson, style, gender } = elements;

        const resolutions = {
            kindness: gender === 'girl'
                ? `قرَّرت ${protagonist} أن تكونَ لطيفةً مع الحيوان الصغير، فأصبحا صديقين مقرَّبين. `
                : `قرَّر ${protagonist} أن يكونَ لطيفاً مع الحيوان الصغير، فأصبحا صديقين مقرَّبين. `,

            sharing: gender === 'girl'
                ? `شاركت ${protagonist} الكنزَ مع صديقاتِها، فشعرت بسعادةٍ أكبرَ من أيِّ كنزٍ. `
                : `شارك ${protagonist} الكنزَ مع أصدقائِه، فشعر بسعادةٍ أكبرَ من أيِّ كنزٍ. `,

            courage: gender === 'girl'
                ? `جمعت ${protagonist} شجاعتَها وواجهت التحدي، فاكتشفت أنها أقوى مما كانت تظنُّ. `
                : `جمع ${protagonist} شجاعتَه وواجه التحدي، فاكتشف أنه أقوى مما كان يظنُّ. `,

            honesty: gender === 'girl'
                ? `اختارت ${protagonist} قولَ الحقيقة، وتعلَّمت أن الصدقَ دائماً هو الطريقُ الأفضلُ. `
                : `اختار ${protagonist} قولَ الحقيقة، وتعلَّم أن الصدقَ دائماً هو الطريقُ الأفضلُ. `,

            perseverance: gender === 'girl'
                ? `لم تستسلم ${protagonist} وواصلت المحاولةَ حتى نجحت في النهاية. `
                : `لم يستسلم ${protagonist} وواصل المحاولةَ حتى نجح في النهاية. `,

            friendship: gender === 'girl'
                ? `تعرَّفت ${protagonist} على الطفلة الجديدة واكتشفت أنها صديقةٌ رائعةٌ. `
                : `تعرَّف ${protagonist} على الطفل الجديد واكتشف أنه صديقٌ رائعٌ. `,

            responsibility: gender === 'girl'
                ? `أدركت ${protagonist} أهميةَ المسؤولية وأنجزت مهمتَها بإتقانٍ. `
                : `أدرك ${protagonist} أهميةَ المسؤولية وأنجز مهمتَه بإتقانٍ. `,

            empathy: gender === 'girl'
                ? `تعلَّمت ${protagonist} كيف تفهمُ مشاعرَ الآخرين وتساعدُهم. `
                : `تعلَّم ${protagonist} كيف يفهمُ مشاعرَ الآخرين ويساعدُهم. `,

            confidence: gender === 'girl'
                ? `اكتشفت ${protagonist} قدراتِها الخاصةَ وأصبحت واثقةً من نفسِها. `
                : `اكتشف ${protagonist} قدراتِه الخاصةَ وأصبح واثقاً من نفسِه. `,

            gratitude: gender === 'girl'
                ? `تعلَّمت ${protagonist} أن تشكرَ اللهَ على كلِّ النعم من حولِها. `
                : `تعلَّم ${protagonist} أن يشكرَ اللهَ على كلِّ النعم من حولِه. `
        };

        return resolutions[lesson] || (gender === 'girl'
            ? `تعلَّمت ${protagonist} درساً مهماً غيَّر حياتَها للأفضل. `
            : `تعلَّم ${protagonist} درساً مهماً غيَّر حياتَه للأفضل. `);
    }

    getGrammaticallyCorrectPeacefulEnding(elements) {
        const { protagonist, style, gender } = elements;

        const endings = {
            funny: gender === 'girl'
                ? `وهكذا انتهت مغامرةُ ${protagonist} المضحكةُ، ونامت بسعادةٍ وهي تحلمُ بمغامراتٍ جديدةٍ. `
                : `وهكذا انتهت مغامرةُ ${protagonist} المضحكةُ، ونام بسعادةٍ وهو يحلمُ بمغامراتٍ جديدةٍ. `,

            gentle: gender === 'girl'
                ? `وعادت ${protagonist} إلى منزلِها سعيدةً، ونامت نوماً هادئاً مليئاً بالأحلام الجميلة. `
                : `وعاد ${protagonist} إلى منزلِه سعيداً، ونام نوماً هادئاً مليئاً بالأحلام الجميلة. `,

            exciting: gender === 'girl'
                ? `وهكذا انتهت مغامرةُ ${protagonist} المثيرةُ، وهي تتطلعُ لمغامراتٍ جديدةٍ في المستقبل. `
                : `وهكذا انتهت مغامرةُ ${protagonist} المثيرةُ، وهو يتطلعُ لمغامراتٍ جديدةٍ في المستقبل. `,

            magical: gender === 'girl'
                ? `وعاشت ${protagonist} في سعادةٍ وهناءٍ، محاطةً بالسحر والجمال في كلِّ مكانٍ. `
                : `وعاش ${protagonist} في سعادةٍ وهناءٍ، محاطاً بالسحر والجمال في كلِّ مكانٍ. `
        };

        const baseEnding = endings[style] || (gender === 'girl'
            ? `ونامت ${protagonist} نوماً هادئاً، وهي سعيدةٌ بما تعلَّمته في ذلك اليوم. `
            : `ونام ${protagonist} نوماً هادئاً، وهو سعيدٌ بما تعلَّمه في ذلك اليوم. `);

        const finalBlessing = gender === 'girl'
            ? 'وعاشت في سعادةٍ وهناءٍ إلى الأبد.'
            : 'وعاش في سعادةٍ وهناءٍ إلى الأبد.';

        return baseEnding + ' ' + finalBlessing;
    }

    integrateAdditionalDetails(additionalDetails, gender) {
        // Ensure additional details are grammatically integrated
        if (!additionalDetails) return '';

        // Clean and properly format additional details
        let details = additionalDetails.trim();
        if (!details.endsWith('.') && !details.endsWith('،') && !details.endsWith('؛')) {
            details += '.';
        }

        return details;
    }

    performLinguisticReview(content) {
        // Comprehensive linguistic review system
        let reviewedContent = content;

        // 1. Fix common grammatical patterns
        reviewedContent = this.fixCommonGrammaticalErrors(reviewedContent);

        // 2. Ensure proper diacritical marks where needed
        reviewedContent = this.addEssentialDiacritics(reviewedContent);

        // 3. Verify gender agreement consistency
        reviewedContent = this.verifyGenderAgreement(reviewedContent);

        // 4. Check and fix punctuation
        reviewedContent = this.fixArabicPunctuation(reviewedContent);

        // 5. Final spelling and structure check
        reviewedContent = this.finalSpellingCheck(reviewedContent);

        return reviewedContent;
    }

    fixCommonGrammaticalErrors(text) {
        // Fix common Arabic grammatical mistakes
        let fixed = text;

        // Fix hamza patterns
        fixed = fixed.replace(/أ([ءئؤإأآ])/g, 'أ$1');
        fixed = fixed.replace(/([^ا])ء([اوي])/g, '$1ئ$2');

        // Fix ta marbuta vs ta maftuha
        fixed = fixed.replace(/ة([^ً\s])/g, 'ت$1');
        fixed = fixed.replace(/ت\s/g, 'ة ');
        fixed = fixed.replace(/ت\./g, 'ة.');
        fixed = fixed.replace(/ت،/g, 'ة،');

        // Fix alif patterns
        fixed = fixed.replace(/([يو])ا\s/g, '$1ى ');
        fixed = fixed.replace(/([يو])ا\./g, '$1ى.');
        fixed = fixed.replace(/([يو])ا،/g, '$1ى،');

        return fixed;
    }

    addEssentialDiacritics(text) {
        // Add essential diacritics for clarity and correctness
        let diacriticized = text;

        // Add diacritics to common words for clarity
        const diacriticMap = {
            'كان': 'كانَ',
            'كانت': 'كانتْ',
            'هو': 'هُوَ',
            'هي': 'هِيَ',
            'في': 'فِي',
            'من': 'مِنْ',
            'إلى': 'إِلَى',
            'على': 'عَلَى',
            'مع': 'مَعَ',
            'كل': 'كُلُّ',
            'كله': 'كُلُّهُ',
            'كلها': 'كُلُّهَا',
            'هذا': 'هَذَا',
            'هذه': 'هَذِهِ',
            'ذلك': 'ذَلِكَ',
            'تلك': 'تِلْكَ'
        };

        Object.keys(diacriticMap).forEach(word => {
            const regex = new RegExp(`\\b${word}\\b`, 'g');
            diacriticized = diacriticized.replace(regex, diacriticMap[word]);
        });

        return diacriticized;
    }

    verifyGenderAgreement(text) {
        // This is a simplified gender agreement check
        // In a full implementation, this would be much more sophisticated
        let verified = text;

        // Check for common gender disagreement patterns and fix them
        // This is a basic implementation - a full system would need NLP

        return verified;
    }

    fixArabicPunctuation(text) {
        // Fix Arabic punctuation marks
        let fixed = text;

        // Replace English punctuation with Arabic equivalents where appropriate
        fixed = fixed.replace(/,/g, '،');
        fixed = fixed.replace(/;/g, '؛');
        fixed = fixed.replace(/\?/g, '؟');

        // Ensure proper spacing around punctuation
        fixed = fixed.replace(/\s+،/g, '،');
        fixed = fixed.replace(/،([^\s])/g, '، $1');
        fixed = fixed.replace(/\s+\./g, '.');
        fixed = fixed.replace(/\.([^\s])/g, '. $1');

        // Fix multiple spaces
        fixed = fixed.replace(/\s+/g, ' ');

        return fixed;
    }

    finalSpellingCheck(text) {
        // Final spelling verification
        let checked = text;

        // Common spelling corrections
        const spellingCorrections = {
            'إنشاء الله': 'إن شاء الله',
            'الحمد لله': 'الحمدُ لله',
            'بإذن الله': 'بإذن الله',
            'ماشاء الله': 'ما شاء الله'
        };

        Object.keys(spellingCorrections).forEach(wrong => {
            const regex = new RegExp(wrong, 'g');
            checked = checked.replace(regex, spellingCorrections[wrong]);
        });

        return checked.trim();
    }

    getMoralMessage(lesson) {
        const morals = {
            kindness: 'اللطفُ مع الآخرين يجعلُ العالمَ مكاناً أجملَ',
            sharing: 'المشاركةُ تُضاعفُ السعادةَ وتُقوِّي الصداقاتِ',
            courage: 'الشجاعةُ تُساعدُنا على تحقيق أحلامِنا',
            honesty: 'الصدقُ هو أساسُ الثقة والاحترامِ',
            perseverance: 'المثابرةُ هي مفتاحُ النجاح في الحياةِ',
            friendship: 'الصداقةُ الحقيقيةُ كنزٌ لا يُقدَّرُ بثمنٍ',
            responsibility: 'المسؤوليةُ تجعلُنا أشخاصاً أفضلَ',
            empathy: 'فهمُ مشاعر الآخرين يُقوِّي روابطَنا معهم',
            confidence: 'الثقةُ بالنفس تفتحُ أبوابَ الفرصِ',
            gratitude: 'الامتنانُ يملأُ القلبَ بالسعادة والرضا'
        };

        return morals[lesson] || 'كلُّ تجربةٍ في الحياة تُعلِّمُنا شيئاً جديداً';
    }

    displayStory(story) {
        const storySection = document.getElementById('story-section');

        storySection.innerHTML = `
            <div class="story-container rounded-3xl p-8 shadow-2xl">
                <div class="text-center mb-8">
                    <h2 class="story-title text-4xl font-bold mb-4 arabic-text">${story.title}</h2>
                    <div class="flex justify-center items-center gap-2 text-purple-600">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="story-content arabic-text mb-8 leading-relaxed">
                    ${story.content}
                </div>

                <div class="bg-gradient-to-r from-purple-100 to-pink-100 rounded-xl p-6 mb-8">
                    <h3 class="text-xl font-bold text-purple-800 mb-3 arabic-text flex items-center">
                        <i class="fas fa-lightbulb ml-2 text-yellow-500"></i>
                        الدرس المستفاد
                    </h3>
                    <p class="text-purple-700 arabic-text font-medium">${story.moral}</p>
                </div>

                <div class="flex flex-wrap justify-center gap-4 mb-8">
                    <button id="read-again-btn" class="btn-primary px-6 py-3 rounded-xl text-white font-medium transition-all duration-300 arabic-text">
                        <i class="fas fa-redo ml-2"></i>
                        اقرأ مرة أخرى
                    </button>
                    <button id="new-story-btn" class="bg-gradient-to-r from-green-500 to-blue-500 px-6 py-3 rounded-xl text-white font-medium transition-all duration-300 arabic-text">
                        <i class="fas fa-plus ml-2"></i>
                        قصة جديدة
                    </button>
                    <button id="save-pdf-btn" class="bg-gradient-to-r from-orange-500 to-red-500 px-6 py-3 rounded-xl text-white font-medium transition-all duration-300 arabic-text">
                        <i class="fas fa-file-pdf ml-2"></i>
                        حفظ PDF
                    </button>
                    <button id="share-btn" class="bg-gradient-to-r from-pink-500 to-purple-500 px-6 py-3 rounded-xl text-white font-medium transition-all duration-300 arabic-text">
                        <i class="fas fa-share-alt ml-2"></i>
                        مشاركة
                    </button>
                </div>

                <div class="text-center text-gray-600 arabic-text">
                    <p class="text-sm">تم إنشاء هذه القصة خصيصاً لطفلك الصغير</p>
                    <p class="text-xs mt-2">© 2025 صانع القصص السحرية</p>
                </div>
            </div>
        `;

        // Bind story action buttons
        this.bindStoryActions(story);
    }

    bindStoryActions(story) {
        document.getElementById('read-again-btn').addEventListener('click', () => {
            this.readStoryAloud(story.content);
        });

        document.getElementById('new-story-btn').addEventListener('click', () => {
            this.resetApplication();
        });

        document.getElementById('save-pdf-btn').addEventListener('click', () => {
            this.generatePDF(story);
        });

        document.getElementById('share-btn').addEventListener('click', () => {
            this.shareStory(story);
        });
    }

    readStoryAloud(content) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(content);
            utterance.lang = 'ar-SA';
            utterance.rate = 0.8;
            utterance.pitch = 1.1;
            speechSynthesis.speak(utterance);
        } else {
            alert('عذراً، متصفحك لا يدعم قراءة النصوص صوتياً');
        }
    }

    generatePDF(story) {
        // Simple PDF generation using browser print
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${story.title}</title>
                <style>
                    body { font-family: 'Tajawal', Arial, sans-serif; line-height: 1.8; margin: 40px; }
                    h1 { color: #8b5cf6; text-align: center; margin-bottom: 30px; }
                    .content { font-size: 16px; margin-bottom: 30px; }
                    .moral { background: #f3f4f6; padding: 20px; border-radius: 10px; border-left: 4px solid #8b5cf6; }
                </style>
            </head>
            <body>
                <h1>${story.title}</h1>
                <div class="content">${story.content}</div>
                <div class="moral">
                    <strong>الدرس المستفاد:</strong> ${story.moral}
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    shareStory(story) {
        if (navigator.share) {
            navigator.share({
                title: story.title,
                text: story.content,
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            const textToCopy = `${story.title}\n\n${story.content}\n\nالدرس المستفاد: ${story.moral}`;
            navigator.clipboard.writeText(textToCopy).then(() => {
                alert('تم نسخ القصة! يمكنك الآن مشاركتها');
            });
        }
    }

    resetApplication() {
        this.currentStep = 0;
        this.formData = {
            age: '',
            gender: '',
            interests: [],
            style: '',
            lesson: '',
            additionalDetails: ''
        };

        // Show welcome section
        document.getElementById('story-section').classList.add('hidden');
        document.getElementById('form-section').classList.add('hidden');
        document.getElementById('welcome-section').classList.remove('hidden');

        // Reset form
        document.getElementById('next-btn').disabled = true;
        this.updateProgress();
    }

    showError(message) {
        const storySection = document.getElementById('story-section');
        storySection.innerHTML = `
            <div class="glass-effect rounded-3xl p-8 shadow-2xl text-center">
                <i class="fas fa-exclamation-triangle text-6xl text-red-400 mb-6"></i>
                <h2 class="text-2xl font-bold text-white mb-4 arabic-text">حدث خطأ</h2>
                <p class="text-purple-200 mb-8 arabic-text">${message}</p>
                <button id="retry-btn" class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-xl font-medium transition-all duration-300 arabic-text">
                    <i class="fas fa-redo ml-2"></i>
                    حاول مرة أخرى
                </button>
            </div>
        `;

        document.getElementById('retry-btn').addEventListener('click', () => {
            this.resetApplication();
        });

        storySection.classList.remove('hidden');
    }
}

// Story Generator Class for enhanced story creation
class StoryGenerator {
    constructor() {
        // Initialize story generator with new systems
        this.endingsSystem = new StoryEndingsSystem();
        this.dialogueSystem = new AdvancedDialogueSystem();
        this.charactersSystem = new DynamicCharactersSystem();
        this.realisticScenarios = this.endingsSystem.getRealisticScenarios();
        this.interactiveDialogues = this.endingsSystem.getInteractiveDialogues();
    }

    generateEnhancedStory(elements) {
        // Generate linguistically accurate story
        const title = this.generateGrammaticallyCorrectTitle(elements);
        const content = this.buildLinguisticallyAccurateNarrative(elements);
        const moralMessage = this.getEnhancedMoralMessage(elements.lesson, elements.gender);

        return {
            title: title,
            content: content,
            moralMessage: moralMessage
        };
    }

    // Enhanced story generation with advanced options
    generateAdvancedStory(storyData) {
        const elements = {
            protagonist: storyData.characterName,
            age: storyData.age,
            gender: storyData.gender,
            interests: storyData.interests || [],
            style: storyData.storyStyle,
            lesson: storyData.lesson,
            setting: storyData.setting || 'forest',
            length: storyData.storyLength || 'medium',
            language: storyData.language || 'formal',
            additionalDetails: storyData.additionalDetails || ''
        };

        // Generate realistic story with interactive dialogues
        return this.generateRealisticStoryWithDialogues(elements);
    }

    // ===== توليد قصص واقعية مع حوارات تفاعلية =====

    generateRealisticStoryWithDialogues(elements) {
        // اختيار سيناريو واقعي مناسب
        const scenario = this.selectRealisticScenario(elements);

        // اختيار حيوان محبب للأطفال
        const belovedAnimal = this.selectBelovedAnimal(elements);

        // بناء القصة مع الحوارات
        const storyStructure = this.buildStoryWithDialogues(elements, scenario, belovedAnimal);

        return {
            title: storyStructure.title,
            content: storyStructure.content,
            voiceDirections: storyStructure.voiceDirections,
            characters: storyStructure.characters
        };
    }

    selectRealisticScenario(elements) {
        const scenarios = this.realisticScenarios.family_outings;

        // اختيار السيناريو بناءً على الإعدادات
        if (elements.setting === 'forest' || elements.setting === 'nature') {
            return scenarios[0]; // نزهة عائلية في الغابة
        } else if (elements.setting === 'zoo') {
            return scenarios[1]; // زيارة حديقة الحيوان
        } else {
            return scenarios[2]; // اللعب في الحديقة
        }
    }

    selectBelovedAnimal(elements) {
        const animals = this.realisticScenarios.beloved_animals;

        // اختيار الحيوان بناءً على اهتمامات الطفل
        if (elements.interests.includes('cats') || elements.interests.includes('animals')) {
            return animals[0]; // قط صغير
        } else if (elements.interests.includes('dogs')) {
            return animals[1]; // كلب صغير
        } else {
            return animals[2]; // أرنب صغير
        }
    }

    buildStoryWithDialogues(elements, scenario, animal) {
        const protagonist = elements.protagonist || 'البطل الصغير';
        const gender = elements.gender || 'boy';
        const pronouns = this.getCorrectPronouns(gender);

        // إنشاء شخصيات القصة
        const characters = this.createStoryCharacters(protagonist, gender, scenario);

        // بناء محتوى القصة مع الحوارات
        const content = this.constructStoryContent(elements, scenario, animal, characters, pronouns);

        // إضافة توجيهات الصوت
        const voiceDirections = this.createVoiceDirections(characters, animal);

        return {
            title: `${protagonist} و${animal.animal} الضائع`,
            content: content,
            voiceDirections: voiceDirections,
            characters: characters
        };
    }

    createStoryCharacters(protagonist, gender, scenario, age) {
        const characters = [];

        // إنشاء الشخصية الرئيسية باستخدام النظام المتطور
        const protagonistArchetype = this.selectProtagonistArchetype(age);
        const mainCharacter = this.charactersSystem.createDynamicCharacter(
            protagonistArchetype,
            parseInt(age) || 6,
            gender,
            'traditional'
        );
        mainCharacter.name = protagonist;
        mainCharacter.role = 'البطل/البطلة';
        characters.push(mainCharacter);

        // إضافة الشخصيات حسب السيناريو
        scenario.characters.forEach(charType => {
            if (charType === 'الأخت/الأخ') {
                const siblingCharacter = this.createSiblingCharacter(gender, age);
                characters.push(siblingCharacter);
            } else if (charType === 'الوالدان') {
                const parentCharacters = this.createParentCharacters();
                characters.push(...parentCharacters);
            }
        });

        return characters;
    }

    selectProtagonistArchetype(age) {
        const ageNum = parseInt(age) || 6;
        const archetypes = ['the_explorer', 'the_caretaker', 'the_thinker', 'the_entertainer'];

        // اختيار النمط بناءً على العمر
        if (ageNum <= 5) {
            return Math.random() > 0.5 ? 'the_explorer' : 'the_entertainer';
        } else if (ageNum <= 8) {
            return archetypes[Math.floor(Math.random() * archetypes.length)];
        } else {
            return Math.random() > 0.5 ? 'the_thinker' : 'the_caretaker';
        }
    }

    createSiblingCharacter(protagonistGender, age) {
        const siblingGender = protagonistGender === 'girl' ? 'boy' : 'girl';
        const siblingAge = Math.max(3, parseInt(age) - 1 + Math.floor(Math.random() * 3));

        const siblingArchetype = siblingAge < parseInt(age) ? 'the_entertainer' : 'the_caretaker';
        const sibling = this.charactersSystem.createDynamicCharacter(
            siblingArchetype,
            siblingAge,
            siblingGender,
            'traditional'
        );

        sibling.role = siblingAge < parseInt(age) ? 'الأخ/الأخت الصغير' : 'الأخ/الأخت الكبير';
        return sibling;
    }

    createParentCharacters() {
        const father = {
            name: 'بابا',
            role: 'الوالد',
            personality: this.charactersSystem.getFamilyDynamics().parent_child_relationships.wise_father,
            voice_profile: {
                tone: 'عميق ودافئ',
                pace: 'متأني وحكيم',
                volume: 'متوسط ومطمئن'
            },
            dialogue_style: {
                teaching: true,
                encouraging: true,
                wise: true
            }
        };

        const mother = {
            name: 'ماما',
            role: 'الوالدة',
            personality: this.charactersSystem.getFamilyDynamics().parent_child_relationships.loving_mother,
            voice_profile: {
                tone: 'ناعم وحنون',
                pace: 'متوسط ومريح',
                volume: 'دافئ ومحب'
            },
            dialogue_style: {
                nurturing: true,
                protective: true,
                loving: true
            }
        };

        return [father, mother];
    }

    constructStoryContent(elements, scenario, animal, characters, pronouns) {
        let content = '';

        // البداية التقليدية
        content += 'كان يا ما كان، في صباح جميل مشرق، ';

        // السيناريو الأولي
        content += this.buildInitialScenario(elements, scenario, characters, pronouns);

        // الحدث المحوري (الضياع)
        content += this.buildConflictMoment(elements, scenario, characters, pronouns);

        // اكتشاف الحيوان
        content += this.buildAnimalDiscovery(elements, animal, characters, pronouns);

        // الحوارات التفاعلية
        content += this.buildInteractiveDialogues(elements, animal, characters);

        // الحل والعودة
        content += this.buildResolutionWithFamily(elements, animal, characters, pronouns);

        // الخاتمة الواقعية والمشوقة
        content += this.buildRealisticEnding(elements, animal, characters, pronouns);

        return content;
    }

    buildInitialScenario(elements, scenario, characters, pronouns) {
        const protagonist = characters[0].name;

        return `قرر${pronouns.verb_ending} ${protagonist} مع عائلت${pronouns.possessive} الذهاب في ${scenario.initial_situation}.

"يالله يا أطفال، هيا نستعد!" قال بابا بصوت مرح.

"واااو! ${scenario.setting}!" صرخ${pronouns.verb_ending} ${protagonist} بحماس، وهو يقفز من الفرح.

`;
    }

    buildConflictMoment(elements, scenario, characters, pronouns) {
        const protagonist = characters[0].name;

        return `وصلت العائلة، وبدأ ${protagonist} باللعب والاستكشاف.

"${protagonist}! لا تبتعد${pronouns.imperative} كثيراً!" نادت ماما بصوت حنون.

"حاضر ماما!" رد ${protagonist}، لكن${pronouns.possessive} انشغل${pronouns.verb_ending} بمطاردة شيء جميل.

وفجأة، توقف${pronouns.verb_ending} ${protagonist} ونظر${pronouns.verb_ending} حول${pronouns.possessive}... لم يعد يرى أهل${pronouns.possessive}!

"ماما؟ بابا؟" ناد${pronouns.verb_ending} بصوت قلق.

`;
    }

    buildAnimalDiscovery(elements, animal, characters, pronouns) {
        const protagonist = characters[0].name;

        return `وبينما ${pronouns.pronoun} يبحث عن طريق العودة، سمع${pronouns.verb_ending} صوتاً ضعيفاً:

"${animal.sounds[0]}... ${animal.sounds[1]}..."

"ما هذا الصوت؟" تساءل${pronouns.verb_ending} ${protagonist} بفضول.

اتبع${pronouns.verb_ending} الصوت حتى وجد${pronouns.verb_ending} ${animal.animal} صغير${animal.animal === 'قط صغير' ? 'اً' : 'اً'}، يبدو ${animal.characteristics.join(' و')}.

"أوه، أيها ${animal.animal} المسكين!" قال${pronouns.verb_ending} ${protagonist} بصوت حنون، وحمل${pronouns.possessive} بلطف.

"${animal.sounds[0]}!" قال ${animal.animal}، وكأنه يشكر${pronouns.possessive}.

`;
    }
    }

    buildInteractiveDialogues(elements, animal, characters) {
        const protagonist = characters[0];
        const age = parseInt(elements.age) || 6;

        let content = '';

        // إنشاء حوار اكتشاف العائلة باستخدام النظام المتطور
        content += this.generateDiscoveryDialogue(protagonist, characters, animal, age);

        // إنشاء حوار الرعاية والقرار
        content += this.generateCareDialogue(protagonist, characters, animal, age);

        // إنشاء حوار الاحتفال والقرار النهائي
        content += this.generateCelebrationDialogue(protagonist, characters, animal, age);

        return content;
    }

    generateDiscoveryDialogue(protagonist, characters, animal, age) {
        const pronouns = this.getCorrectPronouns(protagonist.gender);
        const father = characters.find(c => c.role === 'الوالد');
        const mother = characters.find(c => c.role === 'الوالدة');

        // استخدام نظام الحوارات المتطور
        const fatherDialogue = this.dialogueSystem.generateCharacterDialogue(
            { type: 'wise_father' },
            { type: 'family_reunion' },
            'worry_relief',
            this.getAgeGroup(age)
        );

        const protagonistDialogue = this.dialogueSystem.generateCharacterDialogue(
            protagonist,
            { type: 'discovery_sharing' },
            'excitement',
            this.getAgeGroup(age)
        );

        return `
وفي تلك اللحظة، سمع${pronouns.verb_ending} صوت بابا ينادي:

"${protagonist.name}! ${protagonist.name}! أين أنت يا حبيب${protagonist.gender === 'girl' ? 'تي' : 'ي'}؟"
*(${father?.voice_profile?.tone || 'بصوت قلق ومحب'})*

"بابا! أنا هنا!" صرخ${pronouns.verb_ending} ${protagonist.name} بفرح.
*(${protagonist.voice_profile?.tone_variations?.[0] || 'بصوت مرتفع ومتحمس'})*

جاء بابا وماما مسرعين، وقلوبهما تخفق من القلق.

"الحمد لله أنك بخير!" قالت ماما وهي تحتضن ${protagonist.name} بقوة.
*(${mother?.voice_profile?.tone || 'بصوت مرتاح ومحب'})*

"ماما، انظري! وجدت ${animal.animal} ضائعاً مثلي!" قال${pronouns.verb_ending} ${protagonist.name} وهو يُري ${animal.animal} بحماس.
*(${protagonistDialogue.emotional_tone})*

"${animal.sounds[0]}!" قال ${animal.animal} بصوت ضعيف ولطيف.
*(صوت خافت ومتردد)*

`;
    }

    generateCareDialogue(protagonist, characters, animal, age) {
        const pronouns = this.getCorrectPronouns(protagonist.gender);
        const sibling = characters.find(c => c.role.includes('الأخ') || c.role.includes('الأخت'));
        const father = characters.find(c => c.role === 'الوالد');

        // توليد حوارات متنوعة حسب شخصية كل فرد
        const siblingReaction = this.generateSiblingReaction(sibling, animal, age);
        const fatherWisdom = this.generateFatherWisdom(father, age);
        const protagonistPlea = this.generateProtagonistPlea(protagonist, animal, age);

        return `
${siblingReaction}

"يبدو أنه ${animal.characteristics[1]} ويحتاج رعاية كبيرة," قال بابا وهو ينظر للقط بحنان.
*(${fatherWisdom.voice_direction})*

"هل يمكنني أن آخذه معي؟ سأعتني به وأطعمه وألعب معه كل يوم!" توسل${pronouns.verb_ending} ${protagonist.name} بعيون لامعة مليئة بالأمل.
*(${protagonistPlea.emotional_tone})*

نظر بابا وماما إلى بعضهما نظرة تفاهم، ثم ابتسما ابتسامة دافئة.

"${fatherWisdom.text}" قال بابا بصوت حكيم ومشجع.

`;
    }

    generateCelebrationDialogue(protagonist, characters, animal, age) {
        const pronouns = this.getCorrectPronouns(protagonist.gender);
        const celebrationStyle = this.getCelebrationStyle(protagonist, age);

        return `
"يييييي! شكراً بابا! شكراً ماما!" ${celebrationStyle.action} ${protagonist.name} من الفرح.
*(${celebrationStyle.voice_description})*

"${animal.sounds[0]} ${animal.sounds[1]}!" قال ${animal.animal} وكأنه يشكر الجميع ويعبر عن سعادته.
*(صوت مرح ومرتاح)*

"سنسميه ${this.generateAnimalName(animal)}!" اقترح${pronouns.verb_ending} ${protagonist.name} بحماس.

"اسم جميل ومناسب!" وافقت العائلة بصوت واحد.
*(أصوات متناغمة ومتفقة)*

`;
    }

    generateSiblingReaction(sibling, animal, age) {
        if (!sibling) return '"أوه، إنه لطيف جداً!" قال أحد أفراد العائلة بإعجاب.';

        const reactions = {
            'the_entertainer': `"واااو! انظروا إلى هذا الصغير اللطيف!" صرخ ${sibling.name} بحماس وهو يصفق.
*(بصوت مرح ومتحمس)*`,
            'the_caretaker': `"المسكين، يبدو خائفاً وجائعاً," قال ${sibling.name} بصوت حنون وهو يمد يده بلطف.
*(بنبرة عطوفة ومتعاطفة)*`,
            'the_thinker': `"يبدو أنه صغير جداً، ربما يحتاج رعاية خاصة," لاحظ ${sibling.name} بتأمل.
*(بصوت متفكر ومتأني)*`
        };

        const archetypeKey = Object.keys(this.charactersSystem.getCharacterArchetypes()).find(key =>
            sibling.personality?.core_trait?.includes(key.split('_')[1])
        ) || 'the_entertainer';

        return reactions[archetypeKey] || reactions['the_entertainer'];
    }

    generateFatherWisdom(father, age) {
        const wisdomByAge = {
            young: {
                text: "بما أنك أظهرت قلباً طيباً، يمكنك الاعتناء به",
                voice_direction: "بصوت دافئ ومشجع"
            },
            middle: {
                text: "أرى أنك مستعد لتحمل مسؤولية الاعتناء بصديق جديد",
                voice_direction: "بصوت فخور ومطمئن"
            },
            older: {
                text: "الرحمة بالحيوانات تدل على نضج القلب والعقل، أثق في قدرتك على رعايته",
                voice_direction: "بصوت حكيم ومعتز"
            }
        };

        const ageGroup = this.getAgeGroup(age);
        return wisdomByAge[ageGroup] || wisdomByAge.middle;
    }

    generateProtagonistPlea(protagonist, animal, age) {
        const pleaStyles = {
            'the_caretaker': {
                text: "أعدكم أنني سأعتني به كأنه أخي الصغير",
                emotional_tone: "بصوت صادق ومسؤول"
            },
            'the_explorer': {
                text: "سنكون أفضل الأصدقاء ونخوض مغامرات رائعة معاً!",
                emotional_tone: "بصوت متحمس ومليء بالأحلام"
            },
            'the_entertainer': {
                text: "سألعب معه وأجعله أسعد قط في العالم!",
                emotional_tone: "بصوت مرح ومفعم بالحيوية"
            },
            'the_thinker': {
                text: "لقد فكرت في كل ما يحتاجه، وأنا مستعد لتحمل المسؤولية",
                emotional_tone: "بصوت واثق ومدروس"
            }
        };

        const archetypeKey = Object.keys(this.charactersSystem.getCharacterArchetypes()).find(key =>
            protagonist.personality?.core_trait?.includes(key.split('_')[1])
        ) || 'the_caretaker';

        return pleaStyles[archetypeKey] || pleaStyles['the_caretaker'];
    }

    getCelebrationStyle(protagonist, age) {
        const styles = {
            young: {
                action: "قفز",
                voice_description: "بصوت طفولي عالي ومليء بالبهجة"
            },
            middle: {
                action: "رقص",
                voice_description: "بصوت متحمس ومعبر عن السعادة"
            },
            older: {
                action: "ابتسم بسعادة وعانق",
                voice_description: "بصوت فرح ناضج ومتحكم"
            }
        };

        const ageGroup = this.getAgeGroup(age);
        return styles[ageGroup] || styles.middle;
    }

    getAgeGroup(age) {
        if (age <= 5) return 'young';
        if (age <= 8) return 'middle';
        return 'older';
    }

    buildResolutionWithFamily(elements, animal, characters, pronouns) {
        const protagonist = characters[0].name;
        const animalName = this.generateAnimalName(animal);

        return `"سنسميه ${animalName}!" اقترح أحد أفراد العائلة.

"${animalName}! اسم جميل!" وافق${pronouns.verb_ending} ${protagonist} بحماس.

عادت العائلة إلى البيت، و${protagonist} يحمل ${animalName} بحنان.

أعدت له ماما طعاماً مناسباً، وصنع${pronouns.verb_ending} له ${protagonist} مكاناً مريحاً للنوم.

"${animal.sounds[0]}!" قال ${animalName} وهو يأكل بسعادة.

"أرأيت يا ${animalName}؟ قلت لك إنني سأعتني بك!" قال${pronouns.verb_ending} ${protagonist} وهو يداعب${pronouns.possessive}.

`;
    }

    buildRealisticEnding(elements, animal, characters, pronouns) {
        const protagonist = characters[0].name;
        const animalName = this.generateAnimalName(animal);
        const lesson = this.extractMoralLesson(elements, animal);

        return `ومنذ ذلك اليوم، أصبح ${animalName} جزءاً من العائلة، و${protagonist} يعتني به كل يوم.

${lesson}

وكلما نظر${pronouns.verb_ending} إلى ${animalName} وهو يلعب ويمرح، تذكر${pronouns.verb_ending} ذلك اليوم الجميل، وكيف أن الضياع قاد${pronouns.possessive} إلى صديق جديد رائع.

"${animal.sounds[0]}!" يقول ${animalName} كل صباح، وكأنه يقول "شكراً لك يا ${protagonist}!"

وهكذا انتهت قصتنا الجميلة، وعاش${pronouns.verb_ending} ${protagonist} و${animalName} في سعادة وحب. 🐱💕

---

**توجيهات القراءة التفاعلية:**
- استخدم صوتاً ${characters[0].voice} لـ${protagonist}
- اجعل صوت بابا عميقاً وحنوناً
- صوت ماما دافئ ومحب
- أصوات ${animal.animal}: "${animal.sounds.join('، ')}" بنبرات مختلفة حسب المشاعر
- أضف تأثيرات صوتية: صوت الأقدام "خش خش"، صوت الطبيعة حسب المكان

**نصائح للقارئ:**
- اجعل الطفل يقلد أصوات الشخصيات المختلفة
- توقف عند الحوارات ليشارك الطفل في التمثيل
- اطلب من الطفل تقليد صوت ${animal.animal}
- شجع الطفل على التفاعل مع القصة بالأسئلة والتعليقات`;
    }

    generateAnimalName(animal) {
        const names = {
            'قط صغير': ['مشمش', 'لولو', 'نونو', 'ميمي'],
            'كلب صغير': ['بوبي', 'لاكي', 'تشارلي', 'ماكس'],
            'أرنب صغير': ['أرنوب', 'قطقوط', 'فلفول', 'حبحبوب']
        };

        const animalNames = names[animal.animal] || ['صديق'];
        return animalNames[Math.floor(Math.random() * animalNames.length)];
    }

    extractMoralLesson(elements, animal) {
        const lessons = {
            'قط صغير': 'تعلم أن الرحمة بالحيوانات والاعتناء بها شيء جميل، وأن القلب الطيب يجلب السعادة للجميع.',
            'كلب صغير': 'تعلم أن الوفاء والصداقة من أجمل الصفات، وأن مساعدة الآخرين تجعلنا سعداء.',
            'أرنب صغير': 'تعلم أن اللطف والحنان مع الكائنات الصغيرة يجعل العالم مكاناً أجمل.'
        };

        return lessons[animal.animal] || 'تعلم أن مساعدة الآخرين والعطف عليهم من أجمل الأعمال.';
    }

    createVoiceDirections(characters, animal) {
        return {
            characters: characters.map(char => ({
                name: char.name,
                voice_style: char.voice,
                tips: `استخدم نبرة ${char.voice} عند قراءة حوار ${char.name}`
            })),
            animal: {
                name: animal.animal,
                sounds: animal.sounds,
                tips: `قلد أصوات ${animal.animal} بطريقة لطيفة ومرحة`
            },
            general_tips: [
                'اقرأ بحماس وتفاعل مع الأحداث',
                'غير نبرة صوتك حسب المشاعر',
                'توقف عند الحوارات ليشارك الطفل',
                'استخدم تأثيرات صوتية بسيطة'
            ]
        };
    }

    getCorrectPronouns(gender) {
        if (gender === 'girl') {
            return {
                pronoun: 'هي',
                possessive: 'ها',
                verb_ending: 'ت',
                imperative: 'ي'
            };
        } else {
            return {
                pronoun: 'هو',
                possessive: 'ه',
                verb_ending: '',
                imperative: ''
            };
        }
    }

    generateGrammaticallyCorrectTitle(elements) {
        const protagonist = elements.protagonist;
        const gender = elements.gender;
        const style = elements.style;

        const titleTemplates = {
            'funny': {
                'boy': `مغامراتُ ${protagonist} المضحكةُ`,
                'girl': `مغامراتُ ${protagonist} المضحكةُ`,
                'neutral': `مغامراتُ ${protagonist} المضحكةُ`
            },
            'gentle': {
                'boy': `حكايةُ ${protagonist} الهادئةُ`,
                'girl': `حكايةُ ${protagonist} الهادئةُ`,
                'neutral': `حكايةُ ${protagonist} الهادئةُ`
            },
            'exciting': {
                'boy': `مغامرةُ ${protagonist} المثيرةُ`,
                'girl': `مغامرةُ ${protagonist} المثيرةُ`,
                'neutral': `مغامرةُ ${protagonist} المثيرةُ`
            },
            'magical': {
                'boy': `عالمُ ${protagonist} السحريُّ`,
                'girl': `عالمُ ${protagonist} السحريُّ`,
                'neutral': `عالمُ ${protagonist} السحريُّ`
            }
        };

        return titleTemplates[style]?.[gender] || `قصةُ ${protagonist} الجميلةُ`;
    }

    buildLinguisticallyAccurateNarrative(elements) {
        // الأولوية القصوى: الدقة اللغوية والنحوية والصرفية المطلقة
        // إنشاء قصة بلغة عربية فصحى سليمة 100% مع مراعاة جميع قواعد النحو والصرف والإملاء

        const intro = this.createPerfectGrammarIntroduction(elements.protagonist, elements.gender, this.getSettingDescription(elements.setting));
        const development = this.addCharacterTraitsWithAbsoluteGrammarAccuracy(elements);
        const conflict = this.getGrammaticallyPerfectConflict(elements);
        const resolution = this.getGrammaticallyPerfectResolution(elements);
        const ending = this.getGrammaticallyPerfectPeacefulEnding(elements);

        const fullStory = `${intro}

${development}

${conflict}

${resolution}

${ending}`;

        // تطبيق مراجعة لغوية شاملة ومتقدمة للتأكد من الدقة المطلقة
        return this.performAdvancedLinguisticReview(fullStory, elements);
    }

    createPerfectGrammarIntroduction(protagonist, gender, setting) {
        // إنشاء مقدمة بدقة نحوية مطلقة مع مراعاة التذكير والتأنيث والإعراب الصحيح
        if (gender === 'boy') {
            return `${setting}، كان هناك طفلٌ صغيرٌ اسمُه ${protagonist}. كان ${protagonist} طفلاً ذكيّاً ولطيفاً، يُحبُّه جميعُ مَن يعرفُه في القريةِ. كان يتميّزُ بقلبِه الطيّبِ وابتسامتِه الجميلةِ التي تُضيءُ وجهَه الصغيرَ.`;
        } else if (gender === 'girl') {
            return `${setting}، كانت هناك طفلةٌ صغيرةٌ اسمُها ${protagonist}. كانت ${protagonist} طفلةً ذكيّةً ولطيفةً، يُحبُّها جميعُ مَن يعرفُها في القريةِ. كانت تتميّزُ بقلبِها الطيّبِ وابتسامتِها الجميلةِ التي تُضيءُ وجهَها الصغيرَ.`;
        } else {
            return `${setting}، كان هناك طفلٌ صغيرٌ اسمُه ${protagonist}. كان ${protagonist} طفلاً مميّزاً ولطيفاً، يُحبُّه جميعُ مَن يعرفُه في المكانِ. كان يتميّزُ بقلبِه الطيّبِ وابتسامتِه الجميلةِ التي تُضيءُ وجهَه الصغيرَ.`;
        }
    }

    createGenderAwareIntroduction(protagonist, gender, setting) {
        // الدالة القديمة للتوافق مع الأجزاء الأخرى
        return this.createPerfectGrammarIntroduction(protagonist, gender, setting);
    }

    getSettingDescription(setting) {
        const settings = {
            'forest': 'في غابةٍ سحريةٍ بعيدةٍ',
            'castle': 'في قصرٍ عظيمٍ على تلةٍ عاليةٍ',
            'village': 'في قريةٍ صغيرةٍ هادئةٍ',
            'ocean': 'في أعماق البحر الأزرق',
            'space': 'في الفضاء الواسع بين النجوم',
            'garden': 'في حديقةٍ جميلةٍ مليئةٍ بالأزهار',
            'mountain': 'على جبلٍ عالٍ تحت السماء الصافية',
            'desert': 'في صحراءَ ذهبيةٍ واسعةٍ'
        };
        return settings[setting] || 'في مكانٍ جميلٍ بعيدٍ';
    }

    addCharacterTraitsWithCorrectGrammar(elements) {
        const protagonist = elements.protagonist;
        const gender = elements.gender;
        const interests = elements.interests || [];

        let traits = '';

        if (interests.includes('animals')) {
            if (gender === 'boy') {
                traits += `كان ${protagonist} يُحبُّ الحيواناتِ كثيراً، وكان يقضي وقتَه في مراقبة الطيور والفراشات الملونة. `;
            } else if (gender === 'girl') {
                traits += `كانت ${protagonist} تُحبُّ الحيواناتِ كثيراً، وكانت تقضي وقتَها في مراقبة الطيور والفراشات الملونة. `;
            }
        }

        if (interests.includes('books')) {
            if (gender === 'boy') {
                traits += `وكان ${protagonist} يُحبُّ القراءةَ والتعلُّمَ، فكان يقرأُ الكتبَ الجميلةَ كلَّ يومٍ. `;
            } else if (gender === 'girl') {
                traits += `وكانت ${protagonist} تُحبُّ القراءةَ والتعلُّمَ، فكانت تقرأُ الكتبَ الجميلةَ كلَّ يومٍ. `;
            }
        }

        return traits || `كان ${protagonist} طفلاً مميّزاً يُحبُّ اللعبَ والمرحَ.`;
    }

    getGrammaticallyCorrectConflict(elements) {
        const protagonist = elements.protagonist;
        const gender = elements.gender;

        const conflicts = {
            'animals': {
                'boy': `وفي يومٍ من الأيام، رأى ${protagonist} حيواناً صغيراً يبكي في الغابة. كان الحيوانُ خائفاً ووحيداً.`,
                'girl': `وفي يومٍ من الأيام، رأت ${protagonist} حيواناً صغيراً يبكي في الغابة. كان الحيوانُ خائفاً ووحيداً.`
            },
            'friendship': {
                'boy': `وذات يومٍ، التقى ${protagonist} بطفلٍ جديدٍ في الحي، لكنَّ الطفلَ كان خجولاً ولا يتكلمُ مع أحد.`,
                'girl': `وذات يومٍ، التقت ${protagonist} بطفلةٍ جديدةٍ في الحي، لكنَّ الطفلةَ كانت خجولةً ولا تتكلمُ مع أحد.`
            }
        };

        const mainInterest = elements.interests?.[0] || 'friendship';
        return conflicts[mainInterest]?.[gender] || conflicts['friendship'][gender] ||
               `وفي يومٍ من الأيام، واجه ${protagonist} تحدّياً صعباً.`;
    }

    getGrammaticallyCorrectResolution(elements) {
        const protagonist = elements.protagonist;
        const gender = elements.gender;
        const lesson = elements.lesson;

        const resolutions = {
            'kindness': {
                'boy': `قرَّر ${protagonist} أن يساعدَ بلطفٍ وحنان. اقترب منه بهدوءٍ وقال له كلماتٍ طيبةً.`,
                'girl': `قرَّرت ${protagonist} أن تساعدَ بلطفٍ وحنان. اقتربت منه بهدوءٍ وقالت له كلماتٍ طيبةً.`
            },
            'sharing': {
                'boy': `فكَّر ${protagonist} في الأمر، ثم قرَّر أن يشاركَ ما لديه مع الآخرين.`,
                'girl': `فكَّرت ${protagonist} في الأمر، ثم قرَّرت أن تشاركَ ما لديها مع الآخرين.`
            },
            'courage': {
                'boy': `جمع ${protagonist} شجاعتَه وقرَّر أن يواجهَ الموقفَ بثقةٍ.`,
                'girl': `جمعت ${protagonist} شجاعتَها وقرَّرت أن تواجهَ الموقفَ بثقةٍ.`
            }
        };

        return resolutions[lesson]?.[gender] || resolutions['kindness'][gender] ||
               `وجد ${protagonist} الحلَّ المناسبَ للمشكلة.`;
    }

    getGrammaticallyCorrectPeacefulEnding(elements) {
        const protagonist = elements.protagonist;
        const gender = elements.gender;

        if (gender === 'boy') {
            return `وهكذا، عاد ${protagonist} إلى بيته سعيداً ومرتاحاً. تناول عشاءَه مع عائلته، وحكى لهم عن يومه الجميل. ثم ذهب إلى سريره، وغطَّى نفسَه بالبطانية الدافئة، ونام نوماً هادئاً مليئاً بالأحلام السعيدة.`;
        } else if (gender === 'girl') {
            return `وهكذا، عادت ${protagonist} إلى بيتها سعيدةً ومرتاحةً. تناولت عشاءَها مع عائلتها، وحكت لهم عن يومها الجميل. ثم ذهبت إلى سريرها، وغطَّت نفسَها بالبطانية الدافئة، ونامت نوماً هادئاً مليئاً بالأحلام السعيدة.`;
        } else {
            return `وهكذا، عاد ${protagonist} إلى البيت سعيداً ومرتاحاً. تناول العشاءَ مع العائلة، وحكى عن اليوم الجميل. ثم ذهب إلى السرير، وتغطَّى بالبطانية الدافئة، ونام نوماً هادئاً مليئاً بالأحلام السعيدة.`;
        }
    }

    getEnhancedMoralMessage(lesson, gender) {
        const messages = {
            'kindness': 'اللطفُ مع الآخرين يجعلُ العالمَ مكاناً أجملَ وأكثرَ سعادةً.',
            'sharing': 'المشاركةُ مع الأصدقاء تضاعفُ الفرحةَ وتقوّي الصداقةَ.',
            'courage': 'الشجاعةُ تساعدُنا على مواجهة التحدّيات وتحقيق أحلامنا.',
            'honesty': 'الصدقُ هو أساسُ الثقة والاحترامِ بين الناس.',
            'perseverance': 'المثابرةُ والإصرارُ يقودان إلى النجاح والتميُّز.',
            'friendship': 'الصداقةُ الحقيقيةُ كنزٌ ثمينٌ يجبُ أن نحافظَ عليه.',
            'responsibility': 'تحمُّلُ المسؤولية يجعلُنا أشخاصاً أقوياءَ وموثوقين.',
            'empathy': 'فهمُ مشاعر الآخرين والتعاطفُ معهم يجعلُنا أشخاصاً أفضل.',
            'confidence': 'الثقةُ بالنفس تساعدُنا على تحقيق المستحيل.',
            'gratitude': 'الامتنانُ لما نملكُ يجعلُنا أكثرَ سعادةً ورضاً.',
            'patience': 'الصبرُ مفتاحُ الفرج، وبه نصلُ إلى أهدافنا.',
            'forgiveness': 'التسامحُ يطهّرُ القلبَ ويجلبُ السلامَ للنفس.'
        };

        return messages[lesson] || messages['kindness'];
    }

    performLinguisticReview(content) {
        // Apply comprehensive linguistic review
        let reviewedContent = content;

        // Fix common grammatical errors
        reviewedContent = this.fixCommonGrammaticalErrors(reviewedContent);

        // Add essential diacritics
        reviewedContent = this.addEssentialDiacritics(reviewedContent);

        // Fix Arabic punctuation
        reviewedContent = this.fixArabicPunctuation(reviewedContent);

        // Final spelling check
        reviewedContent = this.finalSpellingCheck(reviewedContent);

        return reviewedContent;
    }

    fixCommonGrammaticalErrors(text) {
        return text
            .replace(/ة\s/g, 'ةً ')  // Fix ta marbuta
            .replace(/ه\s/g, 'هُ ')   // Fix ha endings
            .replace(/ي\s/g, 'ي ')    // Fix ya endings
            .replace(/\s+/g, ' ')     // Fix multiple spaces
            .trim();
    }

    addEssentialDiacritics(text) {
        return text
            .replace(/كان\s/g, 'كان ')
            .replace(/كانت\s/g, 'كانت ')
            .replace(/قال\s/g, 'قال ')
            .replace(/قالت\s/g, 'قالت ');
    }

    fixArabicPunctuation(text) {
        return text
            .replace(/,/g, '،')      // Replace comma
            .replace(/;/g, '؛')      // Replace semicolon
            .replace(/\?/g, '؟');    // Replace question mark
    }

    finalSpellingCheck(text) {
        return text
            .replace(/إ/g, 'إ')      // Ensure proper hamza
            .replace(/أ/g, 'أ')      // Ensure proper hamza
            .replace(/ؤ/g, 'ؤ')      // Ensure proper hamza
            .replace(/ئ/g, 'ئ');     // Ensure proper hamza
    }

    // Additional methods for advanced story generation
    generateShortStory(elements) {
        const title = this.generateGrammaticallyCorrectTitle(elements);
        const intro = this.createGenderAwareIntroduction(elements.protagonist, elements.gender, this.getSettingDescription(elements.setting));
        const conflict = this.getGrammaticallyCorrectConflict(elements);
        const resolution = this.getGrammaticallyCorrectResolution(elements);
        const ending = this.getGrammaticallyCorrectPeacefulEnding(elements);
        const moralMessage = this.getEnhancedMoralMessage(elements.lesson, elements.gender);

        const content = `${intro}

${conflict}

${resolution}

${ending}`;

        return {
            title: title,
            content: this.performLinguisticReview(content),
            moralMessage: moralMessage
        };
    }

    generateLongStory(elements) {
        const title = this.generateGrammaticallyCorrectTitle(elements);
        const intro = this.createGenderAwareIntroduction(elements.protagonist, elements.gender, this.getSettingDescription(elements.setting));
        const development = this.createStoryDevelopment(elements);
        const conflict = this.getGrammaticallyCorrectConflict(elements);
        const climax = this.createStoryClimax(elements);
        const resolution = this.getGrammaticallyCorrectResolution(elements);
        const ending = this.getGrammaticallyCorrectPeacefulEnding(elements);
        const moralMessage = this.getEnhancedMoralMessage(elements.lesson, elements.gender);

        const content = `${intro}

${development}

${conflict}

${climax}

${resolution}

${ending}`;

        return {
            title: title,
            content: this.performLinguisticReview(content),
            moralMessage: moralMessage
        };
    }

    createStoryDevelopment(elements) {
        const developments = {
            'animals': `كان ${elements.protagonist} يُحبُّ الحيوانات كثيراً، وكان يقضي وقته في مراقبة الطيور والفراشات الملونة.`,
            'space': `كان ${elements.protagonist} يحلم بالسفر إلى النجوم واستكشاف الكواكب البعيدة.`,
            'magic': `اكتشف ${elements.protagonist} أن لديه قوى سحرية خاصة يمكنها مساعدة الآخرين.`,
            'adventure': `كان ${elements.protagonist} مغامراً شجاعاً يحب استكشاف الأماكن الجديدة.`
        };

        const mainInterest = elements.interests[0] || 'adventure';
        return developments[mainInterest] || developments['adventure'];
    }

    createStoryClimax(elements) {
        const climaxes = {
            'funny': `وفجأة، حدث شيءٌ مضحكٌ جداً جعل الجميع يضحكون بسعادة.`,
            'exciting': `وفي اللحظة الحاسمة، اتخذ ${elements.protagonist} قراراً شجاعاً غيّر كل شيء.`,
            'magical': `وعندها، حدثت معجزة سحرية رائعة أضاءت المكان بنورٍ ذهبي.`,
            'gentle': `وبهدوء ولطف، وجد ${elements.protagonist} الحل المثالي للمشكلة.`
        };

        return climaxes[elements.style] || climaxes['gentle'];
    }

    simplifyLanguage(content) {
        // Simplify complex words and structures for younger children
        return content
            .replace(/الذي|التي/g, 'اللي')
            .replace(/كثيراً/g, 'كتير')
            .replace(/جميلة/g, 'حلوة')
            .replace(/رائع/g, 'حلو');
    }

    incorporateAdditionalDetails(content, details) {
        // Add additional details to the story
        const sentences = content.split('.');
        const insertIndex = Math.floor(sentences.length / 2);
        sentences.splice(insertIndex, 0, ` ${details}`);
        return sentences.join('.');
    }

    // ===== دوال الدقة اللغوية المطلقة =====

    addCharacterTraitsWithAbsoluteGrammarAccuracy(elements) {
        const protagonist = elements.protagonist;
        const gender = elements.gender;
        const interests = elements.interests || [];

        let traits = '';

        if (interests.includes('animals')) {
            if (gender === 'boy') {
                traits += `كان ${protagonist} يُحبُّ الحيواناتِ حبّاً شديداً، وكان يقضي ساعاتِ يومِه الطويلةَ في مراقبةِ الطيورِ الملوّنةِ والفراشاتِ الرقيقةِ وهي تطيرُ بين الأزهارِ. `;
            } else if (gender === 'girl') {
                traits += `كانت ${protagonist} تُحبُّ الحيواناتِ حبّاً شديداً، وكانت تقضي ساعاتِ يومِها الطويلةَ في مراقبةِ الطيورِ الملوّنةِ والفراشاتِ الرقيقةِ وهي تطيرُ بين الأزهارِ. `;
            }
        }

        if (interests.includes('space')) {
            if (gender === 'boy') {
                traits += `كان ${protagonist} مولعاً بالنجومِ والكواكبِ، وكان يحلمُ بأن يصبحَ رائدَ فضاءٍ يوماً ما ليستكشفَ أسرارَ الكونِ الواسعِ. `;
            } else if (gender === 'girl') {
                traits += `كانت ${protagonist} مولعةً بالنجومِ والكواكبِ، وكانت تحلمُ بأن تصبحَ رائدةَ فضاءٍ يوماً ما لتستكشفَ أسرارَ الكونِ الواسعِ. `;
            }
        }

        if (interests.includes('books')) {
            if (gender === 'boy') {
                traits += `كان ${protagonist} قارئاً نهماً، يُحبُّ الكتبَ والحكاياتِ، وكان يقضي ساعاتٍ طويلةً غارقاً في عوالمِ القصصِ الخياليةِ. `;
            } else if (gender === 'girl') {
                traits += `كانت ${protagonist} قارئةً نهمةً، تُحبُّ الكتبَ والحكاياتِ، وكانت تقضي ساعاتٍ طويلةً غارقةً في عوالمِ القصصِ الخياليةِ. `;
            }
        }

        return traits || this.getDefaultCharacterTraits(protagonist, gender);
    }

    getDefaultCharacterTraits(protagonist, gender) {
        if (gender === 'boy') {
            return `كان ${protagonist} طفلاً فضولياً ومحبّاً للاستطلاعِ، يسألُ عن كلِّ شيءٍ يراه ويريدُ أن يتعلّمَ المزيدَ عن العالمِ من حولِه. `;
        } else if (gender === 'girl') {
            return `كانت ${protagonist} طفلةً فضوليةً ومحبّةً للاستطلاعِ، تسألُ عن كلِّ شيءٍ تراه وتريدُ أن تتعلّمَ المزيدَ عن العالمِ من حولِها. `;
        } else {
            return `كان ${protagonist} طفلاً فضولياً ومحبّاً للاستطلاعِ، يسألُ عن كلِّ شيءٍ يراه ويريدُ أن يتعلّمَ المزيدَ عن العالمِ من حولِه. `;
        }
    }

    getGrammaticallyPerfectConflict(elements) {
        const protagonist = elements.protagonist;
        const gender = elements.gender;
        const lesson = elements.lesson;

        const conflicts = {
            'kindness': {
                'boy': `في يومٍ من الأيامِ، بينما كان ${protagonist} يتجوّلُ في الحديقةِ، سمعَ صوتَ بكاءٍ خفيفٍ يأتي من خلفِ الشجرةِ الكبيرةِ. اقتربَ ${protagonist} بحذرٍ، فوجدَ عصفوراً صغيراً قد سقطَ من عشِّه وكان يبدو خائفاً ووحيداً.`,
                'girl': `في يومٍ من الأيامِ، بينما كانت ${protagonist} تتجوّلُ في الحديقةِ، سمعت صوتَ بكاءٍ خفيفٍ يأتي من خلفِ الشجرةِ الكبيرةِ. اقتربت ${protagonist} بحذرٍ، فوجدت عصفوراً صغيراً قد سقطَ من عشِّه وكان يبدو خائفاً ووحيداً.`
            },
            'courage': {
                'boy': `ذاتَ ليلةٍ مظلمةٍ، سمعَ ${protagonist} أصواتاً غريبةً تأتي من الغابةِ القريبةِ من منزلِه. كان الجميعُ نائمين، لكنّ ${protagonist} شعرَ بالقلقِ على الحيواناتِ الصغيرةِ التي قد تكونُ في خطرٍ.`,
                'girl': `ذاتَ ليلةٍ مظلمةٍ، سمعت ${protagonist} أصواتاً غريبةً تأتي من الغابةِ القريبةِ من منزلِها. كان الجميعُ نائمين، لكنّ ${protagonist} شعرت بالقلقِ على الحيواناتِ الصغيرةِ التي قد تكونُ في خطرٍ.`
            },
            'sharing': {
                'boy': `في المدرسةِ، لاحظَ ${protagonist} أنّ زميلَه الجديدَ يجلسُ وحيداً في زاويةِ الفناءِ، ولا يملكُ أيّ ألعابٍ أو كتبٍ ليلعبَ بها مثلَ باقي الأطفالِ.`,
                'girl': `في المدرسةِ، لاحظت ${protagonist} أنّ زميلتَها الجديدةَ تجلسُ وحيدةً في زاويةِ الفناءِ، ولا تملكُ أيّ ألعابٍ أو كتبٍ لتلعبَ بها مثلَ باقي الأطفالِ.`
            }
        };

        const genderKey = gender === 'girl' ? 'girl' : 'boy';
        return conflicts[lesson]?.[genderKey] || conflicts['kindness'][genderKey];
    }

    getGrammaticallyPerfectResolution(elements) {
        const protagonist = elements.protagonist;
        const gender = elements.gender;
        const lesson = elements.lesson;

        const resolutions = {
            'kindness': {
                'boy': `لم يترددْ ${protagonist} لحظةً واحدةً. اقتربَ من العصفورِ الصغيرِ بلطفٍ، ورفعَه بيديه الصغيرتين بعنايةٍ فائقةٍ. ثمّ بحثَ عن عشِّه في الشجرةِ، وبمساعدةِ والدِه، أعادَ العصفورَ الصغيرَ إلى أمِّه التي كانت تنتظرُه بقلقٍ.`,
                'girl': `لم تترددْ ${protagonist} لحظةً واحدةً. اقتربت من العصفورِ الصغيرِ بلطفٍ، ورفعته بيديها الصغيرتين بعنايةٍ فائقةٍ. ثمّ بحثت عن عشِّه في الشجرةِ، وبمساعدةِ والدِها، أعادت العصفورَ الصغيرَ إلى أمِّه التي كانت تنتظرُه بقلقٍ.`
            },
            'courage': {
                'boy': `جمعَ ${protagonist} شجاعتَه، وأخذَ مصباحَه الصغيرَ، وتوجّهَ نحو الغابةِ. اكتشفَ أنّ الأصواتَ كانت تأتي من مجموعةِ حيواناتٍ صغيرةٍ علقت في شبكةِ صيدٍ قديمةٍ. عملَ ${protagonist} بصبرٍ وحكمةٍ حتّى حرّرَ جميعَ الحيواناتِ.`,
                'girl': `جمعت ${protagonist} شجاعتَها، وأخذت مصباحَها الصغيرَ، وتوجّهت نحو الغابةِ. اكتشفت أنّ الأصواتَ كانت تأتي من مجموعةِ حيواناتٍ صغيرةٍ علقت في شبكةِ صيدٍ قديمةٍ. عملت ${protagonist} بصبرٍ وحكمةٍ حتّى حرّرت جميعَ الحيواناتِ.`
            },
            'sharing': {
                'boy': `اقتربَ ${protagonist} من زميلِه الجديدِ بابتسامةٍ دافئةٍ، وقالَ له: "هل تريدُ أن تلعبَ معي؟" ثمّ أخرجَ ألعابَه المفضّلةَ وكتبَه الجميلةَ، وقسّمَها مع صديقِه الجديدِ بسعادةٍ وسرورٍ.`,
                'girl': `اقتربت ${protagonist} من زميلتِها الجديدةِ بابتسامةٍ دافئةٍ، وقالت لها: "هل تريدين أن تلعبي معي؟" ثمّ أخرجت ألعابَها المفضّلةَ وكتبَها الجميلةَ، وقسّمتها مع صديقتِها الجديدةِ بسعادةٍ وسرورٍ.`
            }
        };

        const genderKey = gender === 'girl' ? 'girl' : 'boy';
        return resolutions[lesson]?.[genderKey] || resolutions['kindness'][genderKey];
    }

    getGrammaticallyPerfectPeacefulEnding(elements) {
        const protagonist = elements.protagonist;
        const gender = elements.gender;

        if (gender === 'boy') {
            return `وهكذا تعلّمَ ${protagonist} درساً مهمّاً في ذلك اليومِ، وعادَ إلى منزلِه وقلبُه مليءٌ بالفرحِ والرضا. وعندما حلّ المساءُ، نامَ ${protagonist} نوماً هادئاً وهو يحلمُ بمغامراتٍ جديدةٍ مليئةٍ بالخيرِ والمحبّةِ. وانتهت حكايتُنا الجميلةُ، وعاشَ ${protagonist} سعيداً مع أهلِه وأصدقائِه.`;
        } else if (gender === 'girl') {
            return `وهكذا تعلّمت ${protagonist} درساً مهمّاً في ذلك اليومِ، وعادت إلى منزلِها وقلبُها مليءٌ بالفرحِ والرضا. وعندما حلّ المساءُ، نامت ${protagonist} نوماً هادئاً وهي تحلمُ بمغامراتٍ جديدةٍ مليئةٍ بالخيرِ والمحبّةِ. وانتهت حكايتُنا الجميلةُ، وعاشت ${protagonist} سعيدةً مع أهلِها وأصدقائِها.`;
        } else {
            return `وهكذا تعلّمَ ${protagonist} درساً مهمّاً في ذلك اليومِ، وعادَ إلى منزلِه وقلبُه مليءٌ بالفرحِ والرضا. وعندما حلّ المساءُ، نامَ ${protagonist} نوماً هادئاً وهو يحلمُ بمغامراتٍ جديدةٍ مليئةٍ بالخيرِ والمحبّةِ. وانتهت حكايتُنا الجميلةُ، وعاشَ ${protagonist} سعيداً مع أهلِه وأصدقائِه.`;
        }
    }

    performAdvancedLinguisticReview(content, elements) {
        // مراجعة لغوية متقدمة للتأكد من الدقة المطلقة
        let reviewedContent = content;

        // تصحيح علامات الترقيم والتشكيل
        reviewedContent = this.correctPunctuationAndDiacritics(reviewedContent);

        // التأكد من صحة الإعراب
        reviewedContent = this.ensureCorrectInflection(reviewedContent);

        // مراجعة التذكير والتأنيث
        reviewedContent = this.reviewGenderAgreement(reviewedContent, elements.gender);

        // تصحيح تصريف الأفعال
        reviewedContent = this.correctVerbConjugation(reviewedContent, elements.gender);

        return reviewedContent;
    }

    correctPunctuationAndDiacritics(content) {
        // تصحيح علامات الترقيم والتشكيل الأساسي
        return content
            .replace(/\s+([،؛:.!؟])/g, '$1') // إزالة المسافات قبل علامات الترقيم
            .replace(/([،؛:.!؟])\s*/g, '$1 ') // إضافة مسافة واحدة بعد علامات الترقيم
            .replace(/\s+/g, ' ') // توحيد المسافات
            .trim();
    }

    ensureCorrectInflection(content) {
        // التأكد من صحة الإعراب الأساسي
        return content
            .replace(/في يوم من الايام/g, 'في يومٍ من الأيامِ')
            .replace(/كان هناك طفل/g, 'كان هناك طفلٌ')
            .replace(/كانت هناك طفلة/g, 'كانت هناك طفلةٌ');
    }

    reviewGenderAgreement(content, gender) {
        // مراجعة التذكير والتأنيث
        if (gender === 'girl') {
            content = content
                .replace(/كان يحب/g, 'كانت تحب')
                .replace(/كان يلعب/g, 'كانت تلعب')
                .replace(/ذهب إلى/g, 'ذهبت إلى')
                .replace(/عاد إلى/g, 'عادت إلى');
        }
        return content;
    }

    correctVerbConjugation(content, gender) {
        // تصحيح تصريف الأفعال حسب الجنس
        if (gender === 'girl') {
            content = content
                .replace(/يُحبُّه/g, 'يُحبُّها')
                .replace(/يعرفُه/g, 'يعرفُها')
                .replace(/منزلِه/g, 'منزلِها')
                .replace(/أهلِه/g, 'أهلِها');
        }
        return content;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new BedtimeStoryGenerator();
});