<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط - ركن الأطفال</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            margin: 10px;
            cursor: pointer;
            font-weight: 600;
        }
        .test-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: #f0fff4;
            border-color: #38a169;
            color: #2f855a;
        }
        .error {
            background: #fff5f5;
            border-color: #e53e3e;
            color: #c53030;
        }
        .warning {
            background: #fffbeb;
            border-color: #d69e2e;
            color: #b7791f;
        }
    </style>
</head>
<body>
    <div class="container mx-auto max-w-4xl">
        <h1 class="text-3xl font-bold text-white text-center mb-8">
            🧪 اختبار بسيط للوظائف الأساسية
        </h1>

        <!-- Test Data Collection -->
        <div class="test-card">
            <h2 class="text-xl font-bold mb-4">📊 اختبار جمع البيانات</h2>
            
            <!-- Sample Form -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block mb-2 font-semibold">اسم الطفل:</label>
                    <input type="text" id="child-name" class="w-full p-3 border rounded-lg" placeholder="أحمد" value="أحمد التجريبي">
                </div>
                <div>
                    <label class="block mb-2 font-semibold">العمر:</label>
                    <input type="number" id="child-age" class="w-full p-3 border rounded-lg" placeholder="7" value="7">
                </div>
                <div>
                    <label class="block mb-2 font-semibold">النوع:</label>
                    <select id="child-gender" class="w-full p-3 border rounded-lg">
                        <option value="boy" selected>ولد</option>
                        <option value="girl">بنت</option>
                    </select>
                </div>
                <div>
                    <label class="block mb-2 font-semibold">النبرة المفضلة:</label>
                    <select id="story-tone" class="w-full p-3 border rounded-lg">
                        <option value="gentle">هادئ ولطيف</option>
                        <option value="exciting" selected>مثير ومشوق</option>
                        <option value="educational">تعليمي</option>
                        <option value="funny">مرح</option>
                    </select>
                </div>
            </div>

            <div class="mb-4">
                <label class="block mb-2 font-semibold">عناصر القصة:</label>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <label class="flex items-center">
                        <input type="checkbox" name="elements" value="animals" checked class="ml-2">
                        حيوانات
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="elements" value="adventure" checked class="ml-2">
                        مغامرة
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="elements" value="magic" class="ml-2">
                        سحر
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="elements" value="friendship" checked class="ml-2">
                        صداقة
                    </label>
                </div>
            </div>

            <div class="mb-4">
                <label class="block mb-2 font-semibold">أفكار إضافية:</label>
                <textarea id="user-ideas" class="w-full p-3 border rounded-lg" rows="3" placeholder="اكتب أفكارك هنا...">طفل يحب الحيوانات يجد قطة صغيرة في الحديقة</textarea>
            </div>

            <button class="test-btn" onclick="testDataCollection()">
                <i class="fas fa-database ml-2"></i>
                اختبار جمع البيانات
            </button>
            
            <div id="data-results"></div>
        </div>

        <!-- Test Story Generation -->
        <div class="test-card">
            <h2 class="text-xl font-bold mb-4">📖 اختبار توليد القصص</h2>
            
            <button class="test-btn" onclick="testStoryGeneration()">
                <i class="fas fa-magic ml-2"></i>
                توليد قصة تجريبية
            </button>
            
            <div id="story-results"></div>
        </div>

        <!-- Test Navigation -->
        <div class="test-card">
            <h2 class="text-xl font-bold mb-4">🔗 اختبار التنقل</h2>
            
            <button class="test-btn" onclick="testCollaborativeFlow()">
                <i class="fas fa-rocket ml-2"></i>
                محاكاة التعاون
            </button>
            
            <button class="test-btn" onclick="openCollaborativePage()">
                <i class="fas fa-external-link-alt ml-2"></i>
                فتح صفحة الكتابة التعاونية
            </button>
            
            <div id="nav-results"></div>
        </div>

        <!-- Test Local Storage -->
        <div class="test-card">
            <h2 class="text-xl font-bold mb-4">💾 اختبار التخزين</h2>
            
            <button class="test-btn" onclick="testSaveData()">
                <i class="fas fa-save ml-2"></i>
                حفظ البيانات
            </button>
            
            <button class="test-btn" onclick="testLoadData()">
                <i class="fas fa-download ml-2"></i>
                تحميل البيانات
            </button>
            
            <div id="storage-results"></div>
        </div>
    </div>

    <script>
        function showResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'exclamation-triangle'} ml-2"></i>
                ${message}
            `;
            container.appendChild(result);
        }

        function collectUserInputs() {
            const userInputs = {};
            
            // جمع المدخلات النصية
            const textInputs = document.querySelectorAll('input[type="text"], input[type="number"], textarea, select');
            textInputs.forEach(input => {
                if (input.id) {
                    userInputs[input.id] = input.value || '';
                }
            });
            
            // جمع العناصر المحددة
            const selectedElements = [];
            document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                selectedElements.push({
                    type: checkbox.name || 'element',
                    value: checkbox.value,
                    label: checkbox.value
                });
            });
            
            userInputs.storyElements = selectedElements;
            userInputs.preferences = {
                storyTone: userInputs['story-tone'] || 'gentle',
                storyLength: 'medium',
                language: 'arabic',
                culturalElements: true,
                voiceDirections: true
            };
            
            userInputs.collectionTime = new Date().toISOString();
            
            return userInputs;
        }

        function testDataCollection() {
            try {
                const data = collectUserInputs();
                
                console.log('البيانات المجمعة:', data);
                
                if (data['child-name'] && data['child-age']) {
                    showResult('data-results', 
                        `تم جمع البيانات بنجاح:<br>
                        الاسم: ${data['child-name']}<br>
                        العمر: ${data['child-age']}<br>
                        النبرة: ${data['story-tone']}<br>
                        العناصر: ${data.storyElements.length} عنصر`, 
                        'success'
                    );
                } else {
                    showResult('data-results', 'يرجى ملء الحقول المطلوبة', 'warning');
                }
            } catch (error) {
                showResult('data-results', `خطأ في جمع البيانات: ${error.message}`, 'error');
            }
        }

        function enhanceUserInput(input, settings) {
            let enhanced = input;
            
            // إضافة افتتاحية عربية
            if (!enhanced.startsWith('كان يا ما كان')) {
                enhanced = `كان يا ما كان، ${enhanced}`;
            }
            
            // تطبيق النبرة
            if (settings.preferences.storyTone === 'exciting') {
                enhanced = enhanced.replace(/قال/g, 'صرخ بحماس');
                enhanced = enhanced.replace(/مشى/g, 'جرى بسرعة');
                enhanced += '\n\nوفجأة، بدأت مغامرة مثيرة ومشوقة!';
            } else if (settings.preferences.storyTone === 'gentle') {
                enhanced = enhanced.replace(/قال/g, 'همس بلطف');
                enhanced += '\n\nوفي جو من السكينة والهدوء...';
            }
            
            // إضافة اسم الطفل
            if (settings['child-name']) {
                enhanced = enhanced.replace(/الطفل|البطل/g, settings['child-name']);
            }
            
            // إضافة توجيهات صوتية
            enhanced += '\n\n*(بصوت مرح ومتحمس)*';
            
            // إضافة عناصر تفاعلية
            if (settings.storyElements.some(el => el.value === 'animals')) {
                enhanced += '\n\n🐱 "مياو مياو!" قالت القطة الصغيرة.';
            }
            
            if (settings.storyElements.some(el => el.value === 'adventure')) {
                enhanced += '\n\n🗺️ وهكذا بدأت مغامرة لا تُنسى!';
            }
            
            return enhanced;
        }

        function testStoryGeneration() {
            try {
                const userInputs = collectUserInputs();
                const originalText = userInputs['user-ideas'] || 'طفل يحب الحيوانات';
                
                const enhancedStory = enhanceUserInput(originalText, userInputs);
                
                showResult('story-results', 
                    `<strong>النص الأصلي:</strong><br>${originalText}<br><br>
                    <strong>القصة المحسنة:</strong><br>${enhancedStory.replace(/\n/g, '<br>')}`, 
                    'success'
                );
                
            } catch (error) {
                showResult('story-results', `خطأ في توليد القصة: ${error.message}`, 'error');
            }
        }

        function testCollaborativeFlow() {
            try {
                const userInputs = collectUserInputs();
                
                // إنشاء بيانات التعاون
                const collaborativeData = {
                    mode: 'collaborative',
                    timestamp: new Date().toISOString(),
                    userInputs: userInputs,
                    settings: {
                        language: 'arabic',
                        format: 'interactive',
                        includeVoiceDirections: true,
                        culturalElements: true
                    }
                };
                
                // حفظ البيانات
                localStorage.setItem('collaborativeStoryData', JSON.stringify(collaborativeData));
                
                showResult('nav-results', 
                    'تم حفظ بيانات التعاون بنجاح! يمكنك الآن فتح صفحة الكتابة التعاونية.', 
                    'success'
                );
                
            } catch (error) {
                showResult('nav-results', `خطأ في محاكاة التعاون: ${error.message}`, 'error');
            }
        }

        function openCollaborativePage() {
            try {
                // التأكد من وجود البيانات
                const data = localStorage.getItem('collaborativeStoryData');
                if (!data) {
                    showResult('nav-results', 'يرجى تشغيل محاكاة التعاون أولاً', 'warning');
                    return;
                }
                
                // فتح الصفحة
                window.open('collaborative-writing.html', '_blank');
                showResult('nav-results', 'تم فتح صفحة الكتابة التعاونية في نافذة جديدة', 'success');
                
            } catch (error) {
                showResult('nav-results', `خطأ في فتح الصفحة: ${error.message}`, 'error');
            }
        }

        function testSaveData() {
            try {
                const userInputs = collectUserInputs();
                localStorage.setItem('testUserData', JSON.stringify(userInputs));
                
                showResult('storage-results', 'تم حفظ البيانات في التخزين المحلي بنجاح', 'success');
                
            } catch (error) {
                showResult('storage-results', `خطأ في الحفظ: ${error.message}`, 'error');
            }
        }

        function testLoadData() {
            try {
                const data = localStorage.getItem('testUserData');
                if (data) {
                    const parsed = JSON.parse(data);
                    showResult('storage-results', 
                        `تم تحميل البيانات بنجاح:<br>${JSON.stringify(parsed, null, 2).replace(/\n/g, '<br>')}`, 
                        'success'
                    );
                } else {
                    showResult('storage-results', 'لا توجد بيانات محفوظة', 'warning');
                }
            } catch (error) {
                showResult('storage-results', `خطأ في التحميل: ${error.message}`, 'error');
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            console.log('تم تحميل صفحة الاختبار البسيط');
            
            // اختبار تلقائي للبيانات
            setTimeout(() => {
                testDataCollection();
            }, 1000);
        });
    </script>
</body>
</html>
